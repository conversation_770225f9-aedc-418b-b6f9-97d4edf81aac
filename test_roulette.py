#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour le prédicteur de roulette
"""

from roulette_predictor import RoulettePredictor
import numpy as np

def test_roulette_predictor():
    """Test du prédicteur avec des données d'exemple"""
    print("=== TEST DU PRÉDICTEUR DE ROULETTE ===")
    
    # Créer une instance du prédicteur
    predictor = RoulettePredictor()
    
    # Données d'exemple simulant des tirages réels
    exemple_tirages = [
        12, 5, 18, 22, 3, 36, 15, 7, 29, 14,
        8, 31, 20, 11, 25, 2, 17, 33, 6, 28,
        19, 4, 35, 13, 26, 9, 21, 1, 34, 16,
        10, 27, 0, 24, 30, 23, 32, 18, 7, 15
    ]
    
    print(f"Ajout de {len(exemple_tirages)} tirages d'exemple...")
    predictor.add_numbers(exemple_tirages)
    
    print(f"\nOK {len(predictor.history)} tirages ajoutes a l'historique")
    
    # Test de l'analyse des fréquences
    print("\n=== ANALYSE DES FRÉQUENCES ===")
    freq_analysis = predictor.get_frequency_analysis()
    hot_numbers, cold_numbers = predictor.get_hot_cold_numbers()
    
    print(f"Numéros les plus fréquents:")
    for num, freq in hot_numbers[:5]:
        print(f"  - {num}: {freq:.3f} (attendu: 0.027)")
    
    print(f"Numéros les moins fréquents:")
    for num, freq in cold_numbers[:5]:
        print(f"  - {num}: {freq:.3f} (attendu: 0.027)")
    
    # Test de l'analyse des patterns
    print("\n=== ANALYSE DES PATTERNS ===")
    patterns = predictor.analyze_patterns()
    print(f"5 derniers numéros: {patterns['last_5']}")
    print(f"Numéros les plus communs: {patterns['most_common'][:5]}")
    
    # Test des prédictions
    print("\n=== PRÉDICTIONS ===")
    predictions = predictor.predict_next_numbers(7)
    confidence = predictor.get_prediction_confidence(predictions)
    
    print(f"Prédictions des 7 prochains numéros: {predictions}")
    print(f"Niveau de confiance: {confidence:.1%}")
    
    # Test avec plus de données
    print(f"\n=== TEST AVEC PLUS DE DONNÉES ===")
    
    # Générer plus de données aléatoires pour tester l'adaptation
    nouveaux_tirages = np.random.randint(0, 37, size=50).tolist()
    predictor.add_numbers(nouveaux_tirages)
    
    print(f"Ajout de 50 nouveaux tirages aléatoires")
    print(f"Total historique: {len(predictor.history)} tirages")
    
    # Nouvelles prédictions
    nouvelles_predictions = predictor.predict_next_numbers(7)
    nouvelle_confidence = predictor.get_prediction_confidence(nouvelles_predictions)
    
    print(f"Nouvelles prédictions: {nouvelles_predictions}")
    print(f"Nouvelle confiance: {nouvelle_confidence:.1%}")
    
    # Test de performance
    print(f"\n=== TEST DE PERFORMANCE ===")
    predictor_perf = RoulettePredictor()
    
    # Ajouter beaucoup de données
    gros_dataset = np.random.randint(0, 37, size=500).tolist()
    predictor_perf.add_numbers(gros_dataset)
    
    print(f"Test avec {len(gros_dataset)} tirages")
    predictions_perf = predictor_perf.predict_next_numbers(7)
    confidence_perf = predictor_perf.get_prediction_confidence(predictions_perf)
    
    print(f"Prédictions (gros dataset): {predictions_perf}")
    print(f"Confiance (gros dataset): {confidence_perf:.1%}")
    print(f"Modele ML entraine: {'Oui' if predictor_perf.is_trained else 'Non'}")
    
    return True

def demo_utilisation():
    """Démonstration d'utilisation typique"""
    print("\n" + "="*50)
    print("=== DÉMONSTRATION D'UTILISATION ===")
    print("="*50)
    
    predictor = RoulettePredictor()
    
    # Simulation d'une session de jeu
    print("Simulation d'une session de roulette...")
    
    # Quelques tirages initiaux
    tirages_session = [17, 23, 8, 31, 15, 4, 26, 12, 35, 9]
    
    for i, tirage in enumerate(tirages_session, 1):
        predictor.add_number(tirage)
        print(f"Tirage {i}: {tirage}")
        
        if i >= 5:  # Commencer les prédictions après 5 tirages
            predictions = predictor.predict_next_numbers(7)
            confidence = predictor.get_prediction_confidence(predictions)
            print(f"  -> Predictions: {predictions[:3]}... (confiance: {confidence:.0%})")
    
    print(f"\nAnalyse finale après {len(predictor.history)} tirages:")
    predictor.display_analysis()
    
    return True

if __name__ == "__main__":
    try:
        # Vérifier les dépendances
        print("Vérification des dépendances...")
        import numpy as np
        import pandas as pd
        import sklearn
        print("OK Toutes les dependances sont disponibles")
        
        # Exécuter les tests
        test_roulette_predictor()
        demo_utilisation()
        
        print(f"\nTESTS TERMINES AVEC SUCCES!")
        print(f"Le prédicteur de roulette fonctionne correctement.")
        print(f"\nPour utiliser le programme interactif, exécutez:")
        print(f"python roulette_predictor.py")
        
    except ImportError as e:
        print(f"ERREUR: Dependance manquante - {e}")
        print("Installez les dépendances avec: pip install numpy pandas scikit-learn")
    except Exception as e:
        print(f"ERREUR lors du test: {e}")