#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de reconnaissance d'images pour la roulette
Detecte automatiquement les numeros a partir d'images/captures d'ecran
"""

import cv2
import numpy as np
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
import re
import os
from typing import List, Tuple, Optional

class RouletteImageRecognition:
    def __init__(self):
        self.setup_tesseract()
        
    def setup_tesseract(self):
        """Configure Tesseract OCR"""
        # Chemins communs pour Tesseract sur Windows
        possible_paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
            r"C:\Users\<USER>\Local\Tesseract-OCR\tesseract.exe"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                break
    
    def preprocess_image(self, image_path: str) -> np.ndarray:
        """Preprocess l'image pour ameliorer la reconnaissance OCR"""
        # Lire l'image
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Impossible de lire l'image: {image_path}")
        
        # Convertir en niveaux de gris
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Ameliorer le contraste
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        gray = clahe.apply(gray)
        
        # Appliquer un filtre gaussien pour reduire le bruit
        gray = cv2.GaussianBlur(gray, (3,3), 0)
        
        # Binarisation adaptative
        binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY, 11, 2)
        
        return binary
    
    def detect_numbers_ocr(self, image_path: str) -> List[int]:
        """Detecte les numeros avec OCR"""
        try:
            # Preprocessing
            processed_img = self.preprocess_image(image_path)
            
            # Configuration OCR pour les chiffres
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789'
            
            # Extraction du texte
            text = pytesseract.image_to_string(processed_img, config=custom_config)
            
            # Extraire tous les nombres
            numbers = []
            for match in re.finditer(r'\b([0-9]{1,2})\b', text):
                num = int(match.group(1))
                if 0 <= num <= 36:  # Valide pour la roulette
                    numbers.append(num)
            
            return numbers
            
        except Exception as e:
            print(f"Erreur OCR: {e}")
            return []
    
    def detect_roulette_wheel(self, image_path: str) -> Optional[int]:
        """Detecte le numero sur la roue de roulette"""
        try:
            img = cv2.imread(image_path)
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Detecter les cercles (roue)
            circles = cv2.HoughCircles(gray, cv2.HOUGH_GRADIENT, 1, 20,
                                     param1=50, param2=30, minRadius=0, maxRadius=0)
            
            if circles is not None:
                circles = np.round(circles[0, :]).astype("int")
                
                # Prendre le plus grand cercle (probablement la roue)
                if len(circles) > 0:
                    circle = max(circles, key=lambda c: c[2])  # Rayon maximum
                    x, y, r = circle
                    
                    # Zone centrale de la roue
                    center_region = gray[max(0, y-r//4):min(gray.shape[0], y+r//4),
                                       max(0, x-r//4):min(gray.shape[1], x+r//4)]
                    
                    # OCR sur la zone centrale
                    custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'
                    text = pytesseract.image_to_string(center_region, config=custom_config)
                    
                    numbers = re.findall(r'\b([0-9]{1,2})\b', text)
                    for num_str in numbers:
                        num = int(num_str)
                        if 0 <= num <= 36:
                            return num
            
            return None
            
        except Exception as e:
            print(f"Erreur detection roue: {e}")
            return None
    
    def detect_history_table(self, image_path: str) -> List[int]:
        """Detecte les numeros dans le tableau d'historique"""
        try:
            img = cv2.imread(image_path)
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Detecter les regions rectangulaires (tableaux)
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            numbers = []
            
            for contour in contours:
                # Filtrer les contours trop petits
                area = cv2.contourArea(contour)
                if area < 100:
                    continue
                
                # Obtenir le rectangle englobant
                x, y, w, h = cv2.boundingRect(contour)
                
                # Aspect ratio raisonnable pour un tableau
                aspect_ratio = float(w) / h
                if 0.3 < aspect_ratio < 3.0:
                    # Extraire la region
                    region = gray[y:y+h, x:x+w]
                    
                    # OCR sur la region
                    custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789'
                    text = pytesseract.image_to_string(region, config=custom_config)
                    
                    # Extraire les numeros
                    region_numbers = re.findall(r'\b([0-9]{1,2})\b', text)
                    for num_str in region_numbers:
                        num = int(num_str)
                        if 0 <= num <= 36:
                            numbers.append(num)
            
            return numbers
            
        except Exception as e:
            print(f"Erreur detection historique: {e}")
            return []
    
    def detect_winning_number_display(self, image_path: str) -> Optional[int]:
        """Detecte le numero gagnant affiche (zone specifique)"""
        try:
            img = cv2.imread(image_path)
            
            # Convertir en HSV pour detecter les couleurs
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # Masques pour les couleurs typiques des numeros gagnants
            # Rouge
            lower_red1 = np.array([0, 120, 70])
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([170, 120, 70])
            upper_red2 = np.array([180, 255, 255])
            
            mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
            mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
            mask_red = mask_red1 + mask_red2
            
            # Noir
            lower_black = np.array([0, 0, 0])
            upper_black = np.array([180, 255, 50])
            mask_black = cv2.inRange(hsv, lower_black, upper_black)
            
            # Vert (pour le 0)
            lower_green = np.array([50, 100, 100])
            upper_green = np.array([70, 255, 255])
            mask_green = cv2.inRange(hsv, lower_green, upper_green)
            
            # Combiner les masques
            combined_mask = mask_red + mask_black + mask_green
            
            # Trouver les contours
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 200:  # Zone assez grande
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Extraire et traiter la region
                    region = img[y:y+h, x:x+w]
                    gray_region = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
                    
                    # OCR
                    custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'
                    text = pytesseract.image_to_string(gray_region, config=custom_config)
                    
                    numbers = re.findall(r'\b([0-9]{1,2})\b', text)
                    for num_str in numbers:
                        num = int(num_str)
                        if 0 <= num <= 36:
                            return num
            
            return None
            
        except Exception as e:
            print(f"Erreur detection numero gagnant: {e}")
            return None
    
    def process_image(self, image_path: str) -> dict:
        """Traite une image et retourne tous les numeros detectes"""
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image non trouvee: {image_path}")
        
        results = {
            'file': image_path,
            'ocr_numbers': [],
            'wheel_number': None,
            'history_numbers': [],
            'winning_number': None,
            'all_detected': []
        }
        
        try:
            # Methode 1: OCR general
            results['ocr_numbers'] = self.detect_numbers_ocr(image_path)
            
            # Methode 2: Detection de la roue
            results['wheel_number'] = self.detect_roulette_wheel(image_path)
            
            # Methode 3: Tableau d'historique
            results['history_numbers'] = self.detect_history_table(image_path)
            
            # Methode 4: Numero gagnant
            results['winning_number'] = self.detect_winning_number_display(image_path)
            
            # Combiner tous les resultats
            all_numbers = set()
            all_numbers.update(results['ocr_numbers'])
            if results['wheel_number'] is not None:
                all_numbers.add(results['wheel_number'])
            all_numbers.update(results['history_numbers'])
            if results['winning_number'] is not None:
                all_numbers.add(results['winning_number'])
            
            results['all_detected'] = sorted(list(all_numbers))
            
        except Exception as e:
            print(f"Erreur lors du traitement: {e}")
        
        return results
    
    def batch_process_images(self, image_folder: str) -> List[dict]:
        """Traite toutes les images d'un dossier"""
        if not os.path.exists(image_folder):
            raise FileNotFoundError(f"Dossier non trouve: {image_folder}")
        
        results = []
        supported_formats = ('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif')
        
        for filename in os.listdir(image_folder):
            if filename.lower().endswith(supported_formats):
                image_path = os.path.join(image_folder, filename)
                try:
                    result = self.process_image(image_path)
                    results.append(result)
                except Exception as e:
                    print(f"Erreur avec {filename}: {e}")
        
        return results

def main():
    """Test du module de reconnaissance"""
    print("=== TEST RECONNAISSANCE D'IMAGES ROULETTE ===")
    
    recognizer = RouletteImageRecognition()
    
    # Test avec une image si disponible
    test_image = input("Chemin vers une image de test (ou ENTREE pour passer): ").strip()
    
    if test_image and os.path.exists(test_image):
        try:
            print(f"\nTraitement de: {test_image}")
            results = recognizer.process_image(test_image)
            
            print(f"Resultats:")
            print(f"- OCR general: {results['ocr_numbers']}")
            print(f"- Numero roue: {results['wheel_number']}")
            print(f"- Historique: {results['history_numbers']}")
            print(f"- Numero gagnant: {results['winning_number']}")
            print(f"- Tous detectes: {results['all_detected']}")
            
        except Exception as e:
            print(f"Erreur: {e}")
    else:
        print("Aucune image de test fournie.")
    
    print(f"\nPour utiliser ce module:")
    print(f"1. Installez les dependances: pip install opencv-python pytesseract pillow")
    print(f"2. Installez Tesseract OCR: https://github.com/tesseract-ocr/tesseract")
    print(f"3. Importez: from image_recognition import RouletteImageRecognition")

if __name__ == "__main__":
    main()