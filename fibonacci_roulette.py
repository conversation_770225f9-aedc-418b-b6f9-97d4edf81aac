#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système de Fibonacci pour la Roulette
Aide à gérer les mises selon la stratégie de Fibonacci
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Optional

class FibonacciRouletteSystem:
    def __init__(self, base_bet: float = 1.0, max_sequence_length: int = 15):
        """
        Initialise le système de Fibonacci pour la roulette
        
        Args:
            base_bet: Mise de base (première mise de la séquence)
            max_sequence_length: Longueur maximale de la séquence pour limiter les pertes
        """
        self.base_bet = base_bet
        self.max_sequence_length = max_sequence_length
        self.fibonacci_sequence = self._generate_fibonacci_sequence(max_sequence_length)
        
        # État actuel du système
        self.current_position = 0  # Position dans la séquence de Fibonacci
        self.total_profit = 0.0
        self.session_profit = 0.0
        self.total_bets = 0
        self.wins = 0
        self.losses = 0
        
        # Historique des sessions
        self.bet_history = []
        self.sessions = []
        
        # Configuration des types de paris
        self.bet_types = {
            'rouge': {'payout': 2, 'description': '<PERSON> (1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36)'},
            'noir': {'payout': 2, 'description': 'Noir (2, 4, 6, 8, 10, 11, 13, 15, 17, 20, 22, 24, 26, 28, 29, 31, 33, 35)'},
            'pair': {'payout': 2, 'description': 'Pair (2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36)'},
            'impair': {'payout': 2, 'description': 'Impair (1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35)'},
            'manque': {'payout': 2, 'description': 'Manque (1-18)'},
            'passe': {'payout': 2, 'description': 'Passe (19-36)'},
            'douzaine1': {'payout': 3, 'description': 'Première douzaine (1-12)'},
            'douzaine2': {'payout': 3, 'description': 'Deuxième douzaine (13-24)'},
            'douzaine3': {'payout': 3, 'description': 'Troisième douzaine (25-36)'},
            'colonne1': {'payout': 3, 'description': 'Première colonne (1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34)'},
            'colonne2': {'payout': 3, 'description': 'Deuxième colonne (2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35)'},
            'colonne3': {'payout': 3, 'description': 'Troisième colonne (3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36)'}
        }
        
        # Numéros pour chaque type de pari
        self.bet_numbers = {
            'rouge': [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36],
            'noir': [2, 4, 6, 8, 10, 11, 13, 15, 17, 20, 22, 24, 26, 28, 29, 31, 33, 35],
            'pair': [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36],
            'impair': [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35],
            'manque': list(range(1, 19)),
            'passe': list(range(19, 37)),
            'douzaine1': list(range(1, 13)),
            'douzaine2': list(range(13, 25)),
            'douzaine3': list(range(25, 37)),
            'colonne1': [1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34],
            'colonne2': [2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35],
            'colonne3': [3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36]
        }
    
    def _generate_fibonacci_sequence(self, length: int) -> List[int]:
        """Génère la séquence de Fibonacci"""
        if length <= 0:
            return []
        elif length == 1:
            return [1]
        elif length == 2:
            return [1, 1]
        
        sequence = [1, 1]
        for i in range(2, length):
            sequence.append(sequence[i-1] + sequence[i-2])
        
        return sequence
    
    def get_current_bet_amount(self) -> float:
        """Retourne le montant de la mise actuelle selon la position dans Fibonacci"""
        if self.current_position >= len(self.fibonacci_sequence):
            return self.base_bet * self.fibonacci_sequence[-1]
        return self.base_bet * self.fibonacci_sequence[self.current_position]
    
    def place_bet(self, bet_type: str, number_result: int) -> Dict:
        """
        Place un pari et traite le résultat
        
        Args:
            bet_type: Type de pari ('rouge', 'noir', 'pair', 'impair', etc.)
            number_result: Numéro sorti à la roulette (0-36)
        
        Returns:
            Dict avec les détails du résultat
        """
        if bet_type not in self.bet_types:
            raise ValueError(f"Type de pari invalide: {bet_type}")
        
        if not (0 <= number_result <= 36):
            raise ValueError("Le numéro doit être entre 0 et 36")
        
        bet_amount = self.get_current_bet_amount()
        payout_multiplier = self.bet_types[bet_type]['payout']
        
        # Vérifier si le pari est gagnant
        is_winner = self._check_win(bet_type, number_result)
        
        result = {
            'timestamp': datetime.now().isoformat(),
            'bet_type': bet_type,
            'bet_amount': bet_amount,
            'number_result': number_result,
            'fibonacci_position': self.current_position,
            'is_winner': is_winner,
            'payout_multiplier': payout_multiplier
        }
        
        if is_winner:
            # Gain = (mise × multiplicateur) - mise = mise × (multiplicateur - 1)
            profit = bet_amount * (payout_multiplier - 1)
            result['profit'] = profit
            result['net_result'] = profit
            
            # Mise à jour des statistiques
            self.wins += 1
            self.total_profit += profit
            self.session_profit += profit
            
            # Retour au début de la séquence après un gain
            self.current_position = 0
            result['next_fibonacci_position'] = 0
            result['next_bet_amount'] = self.get_current_bet_amount()
            
        else:
            # Perte = mise
            loss = -bet_amount
            result['profit'] = loss
            result['net_result'] = loss
            
            # Mise à jour des statistiques
            self.losses += 1
            self.total_profit += loss
            self.session_profit += loss
            
            # Avancer dans la séquence de Fibonacci
            self.current_position += 1
            
            # Vérifier si on a atteint la limite
            if self.current_position >= self.max_sequence_length:
                result['sequence_limit_reached'] = True
                result['recommendation'] = "ARRÊTER - Limite de séquence atteinte"
                # Optionnel: reset automatique ou arrêt
                self.current_position = 0
            
            result['next_fibonacci_position'] = self.current_position
            result['next_bet_amount'] = self.get_current_bet_amount()
        
        self.total_bets += 1
        self.bet_history.append(result)
        
        return result
    
    def _check_win(self, bet_type: str, number: int) -> bool:
        """Vérifie si un pari est gagnant"""
        if number == 0:  # Le 0 fait perdre tous les paris simples
            return False
        
        return number in self.bet_numbers.get(bet_type, [])
    
    def get_statistics(self) -> Dict:
        """Retourne les statistiques de la session"""
        win_rate = (self.wins / self.total_bets * 100) if self.total_bets > 0 else 0
        
        return {
            'total_bets': self.total_bets,
            'wins': self.wins,
            'losses': self.losses,
            'win_rate': win_rate,
            'total_profit': self.total_profit,
            'session_profit': self.session_profit,
            'current_fibonacci_position': self.current_position,
            'current_bet_amount': self.get_current_bet_amount(),
            'fibonacci_sequence': self.fibonacci_sequence[:self.current_position + 3]  # Montrer les 3 prochaines
        }
    
    def reset_session(self):
        """Remet à zéro la session actuelle"""
        # Sauvegarder la session actuelle
        if self.bet_history:
            session_data = {
                'start_time': self.bet_history[0]['timestamp'] if self.bet_history else datetime.now().isoformat(),
                'end_time': datetime.now().isoformat(),
                'total_bets': self.total_bets,
                'wins': self.wins,
                'losses': self.losses,
                'session_profit': self.session_profit,
                'bet_history': self.bet_history.copy()
            }
            self.sessions.append(session_data)
        
        # Reset des variables de session
        self.current_position = 0
        self.session_profit = 0.0
        self.total_bets = 0
        self.wins = 0
        self.losses = 0
        self.bet_history = []
    
    def save_data(self, filename: str = "fibonacci_roulette_data.json"):
        """Sauvegarde les données dans un fichier JSON"""
        data = {
            'base_bet': self.base_bet,
            'max_sequence_length': self.max_sequence_length,
            'current_state': {
                'current_position': self.current_position,
                'total_profit': self.total_profit,
                'session_profit': self.session_profit,
                'total_bets': self.total_bets,
                'wins': self.wins,
                'losses': self.losses
            },
            'bet_history': self.bet_history,
            'sessions': self.sessions,
            'last_saved': datetime.now().isoformat()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return f"Données sauvegardées dans {filename}"
    
    def load_data(self, filename: str = "fibonacci_roulette_data.json") -> str:
        """Charge les données depuis un fichier JSON"""
        if not os.path.exists(filename):
            return f"Fichier {filename} non trouvé"
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Restaurer l'état
            self.base_bet = data.get('base_bet', 1.0)
            self.max_sequence_length = data.get('max_sequence_length', 15)
            self.fibonacci_sequence = self._generate_fibonacci_sequence(self.max_sequence_length)
            
            state = data.get('current_state', {})
            self.current_position = state.get('current_position', 0)
            self.total_profit = state.get('total_profit', 0.0)
            self.session_profit = state.get('session_profit', 0.0)
            self.total_bets = state.get('total_bets', 0)
            self.wins = state.get('wins', 0)
            self.losses = state.get('losses', 0)
            
            self.bet_history = data.get('bet_history', [])
            self.sessions = data.get('sessions', [])
            
            return f"Données chargées depuis {filename}"
            
        except Exception as e:
            return f"Erreur lors du chargement: {e}"
    
    def simulate_sequence(self, bet_type: str, win_after_steps: int) -> Dict:
        """
        Simule une séquence de Fibonacci jusqu'à un gain
        
        Args:
            bet_type: Type de pari à simuler
            win_after_steps: Nombre de paris perdants avant le gain
        
        Returns:
            Dict avec les détails de la simulation
        """
        if win_after_steps >= self.max_sequence_length:
            return {"error": "Nombre de steps supérieur à la limite de séquence"}
        
        simulation = {
            'bet_type': bet_type,
            'steps': [],
            'total_bet': 0,
            'final_profit': 0,
            'win_after_steps': win_after_steps
        }
        
        payout_multiplier = self.bet_types[bet_type]['payout']
        
        # Simuler les paris perdants
        for step in range(win_after_steps):
            bet_amount = self.base_bet * self.fibonacci_sequence[step]
            simulation['steps'].append({
                'step': step + 1,
                'fibonacci_number': self.fibonacci_sequence[step],
                'bet_amount': bet_amount,
                'result': 'PERTE',
                'cumulative_loss': simulation['total_bet'] + bet_amount
            })
            simulation['total_bet'] += bet_amount
        
        # Simuler le pari gagnant
        final_step = win_after_steps
        final_bet = self.base_bet * self.fibonacci_sequence[final_step]
        final_win = final_bet * (payout_multiplier - 1)
        
        simulation['steps'].append({
            'step': final_step + 1,
            'fibonacci_number': self.fibonacci_sequence[final_step],
            'bet_amount': final_bet,
            'result': 'GAIN',
            'win_amount': final_win,
            'total_invested': simulation['total_bet'] + final_bet
        })
        
        simulation['total_bet'] += final_bet
        simulation['final_profit'] = final_win - simulation['total_bet'] + final_bet
        simulation['roi'] = (simulation['final_profit'] / simulation['total_bet']) * 100
        
        return simulation
    
    def get_risk_analysis(self) -> Dict:
        """Analyse les risques du système de Fibonacci"""
        analysis = {
            'current_risk_level': 'FAIBLE',
            'max_possible_loss': 0,
            'break_even_probability': 0,
            'recommendations': []
        }
        
        # Calculer la perte maximale possible
        max_loss = sum(self.base_bet * fib for fib in self.fibonacci_sequence)
        analysis['max_possible_loss'] = max_loss
        
        # Évaluer le niveau de risque actuel
        current_exposure = sum(self.base_bet * self.fibonacci_sequence[i] for i in range(self.current_position + 1))
        risk_percentage = (current_exposure / max_loss) * 100
        
        if risk_percentage < 20:
            analysis['current_risk_level'] = 'FAIBLE'
        elif risk_percentage < 50:
            analysis['current_risk_level'] = 'MODÉRÉ'
        elif risk_percentage < 80:
            analysis['current_risk_level'] = 'ÉLEVÉ'
        else:
            analysis['current_risk_level'] = 'TRÈS ÉLEVÉ'
        
        # Probabilité de break-even
        # Pour les paris simples (payout 2), probabilité = 18/37 ≈ 48.65%
        # Pour les douzaines/colonnes (payout 3), probabilité = 12/37 ≈ 32.43%
        base_prob = 18/37  # Assumons des paris simples par défaut
        
        # Probabilité de gagner avant d'atteindre la position actuelle + 1
        prob_win_before_limit = 1 - (1 - base_prob) ** (self.max_sequence_length - self.current_position)
        analysis['break_even_probability'] = prob_win_before_limit * 100
        
        # Recommandations
        if self.current_position > self.max_sequence_length * 0.7:
            analysis['recommendations'].append("ATTENTION: Position élevée dans la séquence")
        
        if risk_percentage > 60:
            analysis['recommendations'].append("Considérer l'arrêt de la session")
        
        if self.session_profit < -max_loss * 0.5:
            analysis['recommendations'].append("Pertes importantes - évaluer la stratégie")
        
        return analysis
    
    def display_current_status(self):
        """Affiche l'état actuel du système"""
        print("=== SYSTÈME DE FIBONACCI - ROULETTE ===")
        print(f"Mise de base: {self.base_bet}€")
        print(f"Position actuelle: {self.current_position + 1}/{self.max_sequence_length}")
        print(f"Prochaine mise: {self.get_current_bet_amount()}€")
        print(f"Nombre de Fibonacci: {self.fibonacci_sequence[self.current_position]}")
        
        print(f"\n=== STATISTIQUES ===")
        stats = self.get_statistics()
        print(f"Total paris: {stats['total_bets']}")
        print(f"Gains: {stats['wins']} | Pertes: {stats['losses']}")
        print(f"Taux de réussite: {stats['win_rate']:.1f}%")
        print(f"Profit total: {stats['total_profit']:.2f}€")
        print(f"Profit session: {stats['session_profit']:.2f}€")
        
        # Analyse des risques
        risk = self.get_risk_analysis()
        print(f"\n=== ANALYSE DES RISQUES ===")
        print(f"Niveau de risque: {risk['current_risk_level']}")
        print(f"Perte maximale possible: {risk['max_possible_loss']:.2f}€")
        print(f"Probabilité de break-even: {risk['break_even_probability']:.1f}%")
        
        if risk['recommendations']:
            print("\nRecommandations:")
            for rec in risk['recommendations']:
                print(f"⚠️  {rec}")
        
        # Afficher la séquence de Fibonacci
        print(f"\n=== SÉQUENCE DE FIBONACCI ===")
        fib_display = []
        for i, fib in enumerate(self.fibonacci_sequence[:10]):  # Afficher les 10 premiers
            marker = " ← ACTUEL" if i == self.current_position else ""
            fib_display.append(f"{i+1}: {fib}{marker}")
        
        print(" | ".join(fib_display))
        
        if len(self.fibonacci_sequence) > 10:
            print("...")


def main():
    """Interface utilisateur principale pour le système de Fibonacci"""
    print("=== SYSTÈME DE FIBONACCI POUR LA ROULETTE ===")
    print("Ce logiciel vous aide à appliquer la stratégie de Fibonacci à la roulette")

    # Configuration initiale
    try:
        base_bet = float(input("Mise de base (€): ") or "1.0")
        max_length = int(input("Longueur maximale de séquence (défaut 15): ") or "15")
    except ValueError:
        print("Valeurs par défaut utilisées: mise de base 1€, séquence max 15")
        base_bet = 1.0
        max_length = 15

    system = FibonacciRouletteSystem(base_bet, max_length)

    # Essayer de charger des données existantes
    load_result = system.load_data()
    print(f"\n{load_result}")

    print("\n=== TYPES DE PARIS DISPONIBLES ===")
    for bet_type, info in system.bet_types.items():
        print(f"{bet_type}: {info['description']} (payout {info['payout']}:1)")

    print("\n=== COMMANDES ===")
    print("- 'bet <type> <numéro>': Placer un pari (ex: 'bet rouge 23')")
    print("- 'status': Afficher l'état actuel")
    print("- 'simulate <type> <steps>': Simuler une séquence (ex: 'simulate rouge 5')")
    print("- 'reset': Nouvelle session")
    print("- 'save': Sauvegarder les données")
    print("- 'help': Afficher l'aide")
    print("- 'quit': Quitter")

    while True:
        try:
            system.display_current_status()
            user_input = input("\n> ").strip().lower()

            if user_input in ['quit', 'exit', 'q']:
                # Sauvegarder avant de quitter
                save_result = system.save_data()
                print(f"\n{save_result}")
                print("Au revoir!")
                break

            elif user_input == 'help':
                print("\n=== AIDE - SYSTÈME DE FIBONACCI ===")
                print("Le système de Fibonacci est une progression positive où:")
                print("- Vous commencez par votre mise de base")
                print("- En cas de perte, vous avancez dans la séquence de Fibonacci")
                print("- En cas de gain, vous revenez au début de la séquence")
                print("- Chaque nombre de Fibonacci détermine votre mise")
                print("\nExemple avec mise de base 1€:")
                print("Pari 1: 1€ (perdu) → Pari 2: 1€ (perdu) → Pari 3: 2€ (perdu)")
                print("→ Pari 4: 3€ (perdu) → Pari 5: 5€ (GAGNÉ) → Retour au début")
                print("\nAvantage: Récupère toutes les pertes + profit avec un seul gain")
                print("Risque: Les mises peuvent devenir très élevées rapidement")

            elif user_input == 'status':
                continue  # Le status s'affiche déjà en boucle

            elif user_input == 'reset':
                confirm = input("Confirmer la remise à zéro de la session? (o/n): ")
                if confirm.lower() in ['o', 'oui', 'y', 'yes']:
                    system.reset_session()
                    print("Session remise à zéro.")

            elif user_input == 'save':
                result = system.save_data()
                print(f"\n{result}")

            elif user_input.startswith('bet '):
                parts = user_input.split()
                if len(parts) >= 3:
                    bet_type = parts[1]
                    try:
                        number = int(parts[2])

                        if bet_type in system.bet_types:
                            result = system.place_bet(bet_type, number)

                            print(f"\n=== RÉSULTAT DU PARI ===")
                            print(f"Type: {result['bet_type']}")
                            print(f"Mise: {result['bet_amount']:.2f}€")
                            print(f"Numéro sorti: {result['number_result']}")
                            print(f"Résultat: {'GAGNÉ' if result['is_winner'] else 'PERDU'}")
                            print(f"Profit/Perte: {result['net_result']:+.2f}€")

                            if result['is_winner']:
                                print("🎉 Félicitations! Retour au début de la séquence.")
                            else:
                                print(f"Prochaine mise: {result['next_bet_amount']:.2f}€")

                                if result.get('sequence_limit_reached'):
                                    print("⚠️ LIMITE DE SÉQUENCE ATTEINTE!")
                                    print("Recommandation: Arrêter ou réduire la mise de base")
                        else:
                            print(f"Type de pari invalide: {bet_type}")
                            print("Types disponibles:", list(system.bet_types.keys()))

                    except ValueError:
                        print("Numéro invalide. Utilisez un nombre entre 0 et 36.")
                else:
                    print("Format: bet <type> <numéro>")
                    print("Exemple: bet rouge 23")

            elif user_input.startswith('simulate '):
                parts = user_input.split()
                if len(parts) >= 3:
                    bet_type = parts[1]
                    try:
                        steps = int(parts[2])

                        if bet_type in system.bet_types:
                            simulation = system.simulate_sequence(bet_type, steps)

                            if "error" in simulation:
                                print(f"Erreur: {simulation['error']}")
                            else:
                                print(f"\n=== SIMULATION - {steps} PERTES PUIS 1 GAIN ===")
                                print(f"Type de pari: {bet_type}")

                                for step in simulation['steps']:
                                    if step['result'] == 'PERTE':
                                        print(f"Étape {step['step']}: {step['bet_amount']:.2f}€ → PERTE (Total: {step['cumulative_loss']:.2f}€)")
                                    else:
                                        print(f"Étape {step['step']}: {step['bet_amount']:.2f}€ → GAIN {step['win_amount']:.2f}€")

                                print(f"\nRésumé:")
                                print(f"Total investi: {simulation['total_bet']:.2f}€")
                                print(f"Profit final: {simulation['final_profit']:+.2f}€")
                                print(f"ROI: {simulation['roi']:+.1f}%")
                        else:
                            print(f"Type de pari invalide: {bet_type}")
                    except ValueError:
                        print("Nombre d'étapes invalide.")
                else:
                    print("Format: simulate <type> <steps>")
                    print("Exemple: simulate rouge 5")

            else:
                print("Commande non reconnue. Tapez 'help' pour l'aide.")

        except KeyboardInterrupt:
            print("\n\nSauvegarde avant fermeture...")
            system.save_data()
            print("Au revoir!")
            break
        except Exception as e:
            print(f"Erreur: {e}")


if __name__ == "__main__":
    main()
