# Guide du Système de Fibonacci pour la Roulette

## Qu'est-ce que le système de Fibonacci ?

Le système de Fibonacci est une stratégie de mise progressive basée sur la célèbre séquence mathématique de Fibonacci : **1, 1, 2, 3, 5, 8, 13, 21, 34, 55...**

### Principe de fonctionnement

1. **Commencez** par votre mise de base (ex: 1€)
2. **En cas de perte** : avancez d'une position dans la séquence
3. **En cas de gain** : revenez au début de la séquence
4. **Chaque nombre** de la séquence détermine votre mise

### Exemple pratique

Avec une mise de base de 1€ sur Rouge :

| Pari | Mise | Résultat | Profit/Perte | Position |
|------|------|----------|--------------|----------|
| 1    | 1€   | Perdu    | -1€          | 2        |
| 2    | 1€   | Perdu    | -2€          | 3        |
| 3    | 2€   | Perdu    | -4€          | 4        |
| 4    | 3€   | Perdu    | -7€          | 5        |
| 5    | 5€   | **GAGNÉ** | **+3€**     | 1        |

**Résultat final** : +3€ de profit après avoir récupéré toutes les pertes !

## Utilisation du logiciel

### Démarrage
```bash
python fibonacci_roulette.py
```

### Configuration initiale
- **Mise de base** : Montant de votre première mise (ex: 1€, 5€, 10€)
- **Longueur max** : Limite de sécurité pour éviter des mises trop élevées (recommandé: 15)

### Commandes principales

#### Placer un pari
```
bet <type> <numéro_sorti>
```

**Types de paris disponibles :**
- `rouge` / `noir` : Couleur (payout 2:1)
- `pair` / `impair` : Parité (payout 2:1)  
- `manque` (1-18) / `passe` (19-36) : Position (payout 2:1)
- `douzaine1` (1-12) / `douzaine2` (13-24) / `douzaine3` (25-36) : Douzaines (payout 3:1)
- `colonne1` / `colonne2` / `colonne3` : Colonnes (payout 3:1)

**Exemples :**
```
bet rouge 23        # Pari sur rouge, numéro sorti : 23
bet pair 14         # Pari sur pair, numéro sorti : 14
bet douzaine1 7     # Pari sur 1ère douzaine, numéro sorti : 7
```

#### Autres commandes
```
status              # Afficher l'état actuel
simulate rouge 5    # Simuler 5 pertes puis 1 gain sur rouge
reset              # Nouvelle session
save               # Sauvegarder les données
help               # Afficher l'aide
quit               # Quitter
```

## Stratégies recommandées

### 1. Paris simples (Rouge/Noir, Pair/Impair)
- **Avantages** : Probabilité élevée (48.65%), mises modérées
- **Recommandé pour** : Débutants, sessions longues

### 2. Douzaines/Colonnes
- **Avantages** : Payout plus élevé (3:1)
- **Inconvénients** : Probabilité plus faible (32.43%), progression plus rapide
- **Recommandé pour** : Joueurs expérimentés avec budget plus important

## Gestion des risques

### Limites de sécurité
- **Position maximale** : Ne jamais dépasser 70% de la séquence
- **Budget session** : Définir un budget maximum avant de commencer
- **Limite de perte** : Arrêter si les pertes dépassent 50% du budget

### Signaux d'alarme
⚠️ **Arrêter immédiatement si :**
- Position > 10 dans la séquence
- Pertes > 50% du budget session
- Mise suivante > 10% du bankroll total

## Avantages du système de Fibonacci

✅ **Points forts :**
- Récupère toutes les pertes avec un seul gain
- Progression moins agressive que la Martingale
- Mathématiquement solide
- Facile à comprendre et appliquer

⚠️ **Limitations :**
- Nécessite un budget conséquent pour les longues séries
- Pas de garantie de gain (la roulette reste un jeu de hasard)
- Les mises peuvent devenir importantes rapidement

## Conseils d'utilisation

### Avant de commencer
1. **Définissez votre budget** total et ne le dépassez jamais
2. **Choisissez votre mise de base** (1-2% du budget total)
3. **Fixez vos limites** de gain et de perte
4. **Testez avec la simulation** avant de jouer réellement

### Pendant le jeu
1. **Suivez strictement** la séquence de Fibonacci
2. **Ne sautez jamais** d'étapes dans la séquence
3. **Arrêtez-vous** aux limites fixées
4. **Prenez des pauses** régulières

### Après la session
1. **Analysez vos résultats** avec les statistiques
2. **Sauvegardez vos données** pour le suivi
3. **Évaluez votre stratégie** et ajustez si nécessaire

## Exemple de session complète

```
=== DÉMARRAGE ===
Mise de base: 2€
Séquence max: 12

=== PARIS ===
bet rouge 15    # Rouge sorti → GAGNÉ +2€ (position 1)
bet rouge 8     # Noir sorti → PERDU -2€ (position 2)  
bet rouge 22    # Rouge sorti → GAGNÉ +2€ (position 1)
bet rouge 0     # Zéro sorti → PERDU -2€ (position 2)
bet rouge 31    # Rouge sorti → GAGNÉ +2€ (position 1)

=== RÉSULTAT ===
Profit total: +2€
Taux de réussite: 60%
```

## Support et dépannage

### Problèmes courants
- **"Type de pari invalide"** : Vérifiez l'orthographe du type de pari
- **"Numéro invalide"** : Le numéro doit être entre 0 et 36
- **"Limite de séquence atteinte"** : Réduisez la mise de base ou augmentez la limite

### Fichiers générés
- `fibonacci_roulette_data.json` : Sauvegarde automatique de vos données
- Contient l'historique complet et les statistiques

---

**Rappel important** : La roulette reste un jeu de hasard. Ce logiciel est un outil d'aide à la gestion des mises selon le système de Fibonacci, mais ne garantit aucun gain. Jouez de manière responsable et dans vos moyens.
