#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prédicteur Physique de Roulette
Analyse la force de lancer et les patterns physiques pour prédire les numéros
"""

import numpy as np
import pandas as pd
from collections import deque, Counter
from typing import List, Dict, Tuple, Optional
import math
from datetime import datetime, timedelta

class RoulettePhysicsPredictor:
    def __init__(self, max_history=500):
        """
        Initialise le prédicteur physique de roulette

        Args:
            max_history: Nombre maximum de tirages à conserver en mémoire
        """
        self.max_history = max_history
        self.history = deque(maxlen=max_history)
        self.timestamps = deque(maxlen=max_history)

        # Configuration de la roulette européenne
        self.wheel_layout = self._create_wheel_layout()
        self.sector_groups = self._create_sector_groups()

        # Paramètres physiques estimés
        self.estimated_wheel_speed = 0.0  # Tours par seconde
        self.estimated_ball_speed = 0.0   # Vitesse relative de la bille
        self.friction_coefficient = 0.02  # Coefficient de friction estimé

        # Historique des forces calculées
        self.force_history = deque(maxlen=max_history)
        self.momentum_history = deque(maxlen=max_history)
        self.velocity_patterns = deque(maxlen=max_history)

    def _create_wheel_layout(self) -> List[int]:
        """Crée la disposition physique de la roulette européenne"""
        return [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10,
                5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26]

    def _create_sector_groups(self) -> Dict[str, List[int]]:
        """Crée des groupes de secteurs pour l'analyse physique"""
        return {
            'voisins_zero': [22, 18, 29, 7, 28, 12, 35, 3, 26, 0, 32, 15, 19, 4, 21, 2, 25],
            'tiers_cylindre': [27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33],
            'orphelins': [1, 20, 14, 31, 9, 17, 34, 6],
            'serie_5_8': [5, 24, 16, 10, 23],
            'serie_0_2_3': [0, 2, 3]
        }

    def add_result(self, number: int, timestamp: Optional[datetime] = None):
        """
        Ajoute un nouveau résultat avec analyse physique

        Args:
            number: Numéro sorti (0-36)
            timestamp: Moment du tirage (optionnel)
        """
        if not (0 <= number <= 36):
            raise ValueError("Le numéro doit être entre 0 et 36")

        if timestamp is None:
            timestamp = datetime.now()

        self.history.append(number)
        self.timestamps.append(timestamp)

        # Calculer les paramètres physiques
        if len(self.history) >= 2:
            self._calculate_physics_parameters()

    def _calculate_physics_parameters(self):
        """Calcule les paramètres physiques basés sur les résultats récents"""
        if len(self.history) < 2:
            return

        # Analyser la distance angulaire entre les numéros consécutifs
        last_numbers = list(self.history)[-10:]  # 10 derniers tirages
        angular_distances = []

        for i in range(len(last_numbers) - 1):
            current_pos = self.wheel_layout.index(last_numbers[i])
            next_pos = self.wheel_layout.index(last_numbers[i + 1])

            # Distance angulaire (plus courte)
            distance = min(abs(next_pos - current_pos),
                          37 - abs(next_pos - current_pos))
            angular_distances.append(distance)

        if angular_distances:
            # Estimer la force de lancer basée sur la consistance des distances
            avg_distance = np.mean(angular_distances)
            distance_variance = np.var(angular_distances)

            # Force estimée (plus la variance est faible, plus la force est consistante)
            estimated_force = max(0.1, 1.0 - (distance_variance / 100))
            self.force_history.append(estimated_force)

            # Momentum basé sur la tendance des distances
            if len(angular_distances) >= 3:
                momentum = np.mean(angular_distances[-3:]) - np.mean(angular_distances[:-3])
                self.momentum_history.append(momentum)

            # Pattern de vélocité
            velocity_pattern = {
                'avg_angular_distance': avg_distance,
                'variance': distance_variance,
                'trend': np.polyfit(range(len(angular_distances)), angular_distances, 1)[0] if len(angular_distances) > 1 else 0
            }
            self.velocity_patterns.append(velocity_pattern)

    def predict_by_physics(self, count: int = 7) -> List[Dict]:
        """
        Prédit les prochains numéros basé sur l'analyse physique

        Args:
            count: Nombre de prédictions à générer

        Returns:
            Liste de prédictions avec scores de confiance
        """
        if len(self.history) < 5:
            return self._random_physics_prediction(count)

        predictions = []

        # Analyser les patterns de force récents
        force_analysis = self._analyze_force_patterns()
        sector_analysis = self._analyze_sector_patterns()
        momentum_analysis = self._analyze_momentum_patterns()

        # Combiner les analyses pour générer des prédictions
        candidate_numbers = self._generate_physics_candidates(
            force_analysis, sector_analysis, momentum_analysis
        )

        # Scorer et trier les candidats
        scored_candidates = []
        for number in candidate_numbers:
            score = self._calculate_physics_score(number, force_analysis,
                                                sector_analysis, momentum_analysis)
            scored_candidates.append({
                'number': number,
                'confidence': score,
                'reasoning': self._get_prediction_reasoning(number, force_analysis,
                                                          sector_analysis, momentum_analysis)
            })

        # Trier par score et retourner les meilleurs
        scored_candidates.sort(key=lambda x: x['confidence'], reverse=True)

        # Éviter les doublons
        seen_numbers = set()
        for candidate in scored_candidates:
            if len(predictions) >= count:
                break
            if candidate['number'] not in seen_numbers:
                predictions.append(candidate)
                seen_numbers.add(candidate['number'])

        # Compléter avec des prédictions aléatoires si nécessaire
        while len(predictions) < count:
            random_num = np.random.randint(0, 37)
            if random_num not in seen_numbers:
                predictions.append({
                    'number': random_num,
                    'confidence': 0.1,
                    'reasoning': 'Prédiction aléatoire de complément'
                })
                seen_numbers.add(random_num)

        return predictions

    def _analyze_force_patterns(self) -> Dict:
        """Analyse les patterns de force de lancer"""
        if not self.force_history:
            return {'avg_force': 0.5, 'force_trend': 0, 'consistency': 0.5}

        forces = list(self.force_history)

        return {
            'avg_force': np.mean(forces),
            'force_trend': np.polyfit(range(len(forces)), forces, 1)[0] if len(forces) > 1 else 0,
            'consistency': 1.0 - np.std(forces),  # Plus l'écart-type est faible, plus c'est consistant
            'recent_force': np.mean(forces[-3:]) if len(forces) >= 3 else np.mean(forces)
        }

    def _analyze_sector_patterns(self) -> Dict:
        """Analyse les patterns par secteur de la roue"""
        if len(self.history) < 5:
            return {}

        recent_numbers = list(self.history)[-20:]  # 20 derniers tirages
        sector_hits = {sector: 0 for sector in self.sector_groups.keys()}

        for number in recent_numbers:
            for sector, numbers in self.sector_groups.items():
                if number in numbers:
                    sector_hits[sector] += 1

        # Calculer les tendances par secteur
        total_hits = sum(sector_hits.values())
        sector_probabilities = {}

        for sector, hits in sector_hits.items():
            expected_hits = len(self.sector_groups[sector]) / 37 * len(recent_numbers)
            deviation = (hits - expected_hits) / expected_hits if expected_hits > 0 else 0
            sector_probabilities[sector] = {
                'hits': hits,
                'expected': expected_hits,
                'deviation': deviation,
                'probability': hits / total_hits if total_hits > 0 else 0
            }

        return sector_probabilities

    def _analyze_momentum_patterns(self) -> Dict:
        """Analyse les patterns de momentum"""
        if not self.momentum_history:
            return {'avg_momentum': 0, 'momentum_trend': 0}

        momentum_values = list(self.momentum_history)

        return {
            'avg_momentum': np.mean(momentum_values),
            'momentum_trend': np.polyfit(range(len(momentum_values)), momentum_values, 1)[0] if len(momentum_values) > 1 else 0,
            'recent_momentum': np.mean(momentum_values[-3:]) if len(momentum_values) >= 3 else np.mean(momentum_values),
            'momentum_acceleration': np.diff(momentum_values[-5:]).mean() if len(momentum_values) >= 5 else 0
        }

    def _generate_physics_candidates(self, force_analysis: Dict, sector_analysis: Dict, momentum_analysis: Dict) -> List[int]:
        """Génère des candidats basés sur l'analyse physique"""
        candidates = []

        if not self.history:
            return list(range(37))

        last_number = self.history[-1]
        last_position = self.wheel_layout.index(last_number)

        # Prédire la distance basée sur la force et le momentum
        estimated_distance = self._estimate_next_distance(force_analysis, momentum_analysis)

        # Générer des candidats autour de la distance estimée
        for offset in range(-3, 4):  # ±3 positions autour de l'estimation
            predicted_position = (last_position + int(estimated_distance) + offset) % 37
            candidates.append(self.wheel_layout[predicted_position])

        # Ajouter des candidats basés sur les secteurs chauds
        for sector, data in sector_analysis.items():
            if data.get('deviation', 0) > 0.2:  # Secteur chaud
                candidates.extend(self.sector_groups[sector][:3])  # 3 premiers numéros du secteur

        return list(set(candidates))  # Éliminer les doublons

    def _estimate_next_distance(self, force_analysis: Dict, momentum_analysis: Dict) -> float:
        """Estime la distance angulaire du prochain tirage"""
        base_distance = 18.5  # Distance moyenne théorique (37/2)

        # Ajuster selon la force
        force_factor = force_analysis.get('recent_force', 0.5)
        force_adjustment = (force_factor - 0.5) * 10  # ±5 positions max

        # Ajuster selon le momentum
        momentum_factor = momentum_analysis.get('recent_momentum', 0)
        momentum_adjustment = momentum_factor * 2  # Influence du momentum

        # Ajouter de la variabilité naturelle
        random_variation = np.random.normal(0, 2)

        estimated_distance = base_distance + force_adjustment + momentum_adjustment + random_variation

        # Limiter dans une plage raisonnable
        return max(5, min(32, estimated_distance))

    def _calculate_physics_score(self, number: int, force_analysis: Dict,
                                sector_analysis: Dict, momentum_analysis: Dict) -> float:
        """Calcule un score de confiance physique pour un numéro"""
        if not self.history:
            return 0.1

        score = 0.0

        # Score basé sur la distance physique prédite
        last_number = self.history[-1]
        last_position = self.wheel_layout.index(last_number)
        number_position = self.wheel_layout.index(number)

        actual_distance = min(abs(number_position - last_position),
                            37 - abs(number_position - last_position))
        estimated_distance = self._estimate_next_distance(force_analysis, momentum_analysis)

        distance_score = max(0, 1.0 - abs(actual_distance - estimated_distance) / 10)
        score += distance_score * 0.4

        # Score basé sur les secteurs
        for sector, data in sector_analysis.items():
            if number in self.sector_groups[sector]:
                sector_score = max(0, data.get('deviation', 0))
                score += sector_score * 0.3

        # Score basé sur la consistance de force
        force_consistency = force_analysis.get('consistency', 0.5)
        if force_consistency > 0.7:  # Force très consistante
            score += 0.2

        # Score basé sur le momentum
        momentum_strength = abs(momentum_analysis.get('recent_momentum', 0))
        if momentum_strength > 1.0:  # Momentum fort
            score += 0.1

        return min(1.0, score)

    def _get_prediction_reasoning(self, number: int, force_analysis: Dict,
                                 sector_analysis: Dict, momentum_analysis: Dict) -> str:
        """Génère une explication pour la prédiction"""
        reasons = []

        # Analyse de distance
        if self.history:
            last_number = self.history[-1]
            last_position = self.wheel_layout.index(last_number)
            number_position = self.wheel_layout.index(number)
            distance = min(abs(number_position - last_position),
                          37 - abs(number_position - last_position))
            reasons.append(f"Distance physique: {distance} cases")

        # Analyse de secteur
        for sector, data in sector_analysis.items():
            if number in self.sector_groups[sector] and data.get('deviation', 0) > 0.1:
                reasons.append(f"Secteur {sector} chaud ({data['deviation']:.2f})")

        # Analyse de force
        force_consistency = force_analysis.get('consistency', 0.5)
        if force_consistency > 0.7:
            reasons.append(f"Force consistante ({force_consistency:.2f})")

        return " | ".join(reasons) if reasons else "Prédiction basée sur patterns généraux"

    def get_physics_analysis(self) -> Dict:
        """Retourne une analyse complète des paramètres physiques"""
        if len(self.history) < 5:
            return {"error": "Pas assez de données pour l'analyse physique"}

        force_analysis = self._analyze_force_patterns()
        sector_analysis = self._analyze_sector_patterns()
        momentum_analysis = self._analyze_momentum_patterns()

        # Analyse des patterns de timing
        timing_analysis = self._analyze_timing_patterns()

        # Analyse de la distribution spatiale
        spatial_analysis = self._analyze_spatial_distribution()

        return {
            'force_analysis': force_analysis,
            'sector_analysis': sector_analysis,
            'momentum_analysis': momentum_analysis,
            'timing_analysis': timing_analysis,
            'spatial_analysis': spatial_analysis,
            'overall_consistency': self._calculate_overall_consistency(),
            'prediction_confidence': self._calculate_prediction_confidence()
        }

    def _analyze_timing_patterns(self) -> Dict:
        """Analyse les patterns temporels"""
        if len(self.timestamps) < 3:
            return {}

        # Calculer les intervalles entre tirages
        intervals = []
        timestamps_list = list(self.timestamps)

        for i in range(1, len(timestamps_list)):
            interval = (timestamps_list[i] - timestamps_list[i-1]).total_seconds()
            intervals.append(interval)

        return {
            'avg_interval': np.mean(intervals) if intervals else 0,
            'interval_variance': np.var(intervals) if intervals else 0,
            'timing_consistency': 1.0 / (1.0 + np.std(intervals)) if intervals else 0.5
        }

    def _analyze_spatial_distribution(self) -> Dict:
        """Analyse la distribution spatiale sur la roue"""
        if len(self.history) < 10:
            return {}

        recent_numbers = list(self.history)[-20:]
        positions = [self.wheel_layout.index(num) for num in recent_numbers]

        # Calculer la dispersion spatiale
        position_variance = np.var(positions)

        # Analyser les clusters
        clusters = self._find_spatial_clusters(positions)

        return {
            'spatial_variance': position_variance,
            'clusters': clusters,
            'distribution_uniformity': self._calculate_uniformity(positions)
        }

    def _find_spatial_clusters(self, positions: List[int]) -> List[Dict]:
        """Trouve les clusters spatiaux sur la roue"""
        clusters = []

        # Grouper les positions proches
        sorted_positions = sorted(positions)
        current_cluster = [sorted_positions[0]]

        for i in range(1, len(sorted_positions)):
            if sorted_positions[i] - sorted_positions[i-1] <= 3:  # Positions proches
                current_cluster.append(sorted_positions[i])
            else:
                if len(current_cluster) >= 3:  # Cluster significatif
                    clusters.append({
                        'positions': current_cluster,
                        'center': np.mean(current_cluster),
                        'size': len(current_cluster)
                    })
                current_cluster = [sorted_positions[i]]

        # Ajouter le dernier cluster
        if len(current_cluster) >= 3:
            clusters.append({
                'positions': current_cluster,
                'center': np.mean(current_cluster),
                'size': len(current_cluster)
            })

        return clusters

    def _calculate_uniformity(self, positions: List[int]) -> float:
        """Calcule l'uniformité de la distribution"""
        if len(positions) < 5:
            return 0.5

        # Diviser la roue en segments et compter les hits
        segments = 8  # 8 segments de ~4.6 positions chacun
        segment_counts = [0] * segments

        for pos in positions:
            segment = int(pos / (37 / segments))
            segment_counts[segment] += 1

        # Calculer l'écart par rapport à une distribution uniforme
        expected_per_segment = len(positions) / segments
        variance = np.var(segment_counts)

        # Uniformité = 1 - variance normalisée
        max_possible_variance = expected_per_segment ** 2
        uniformity = max(0, 1.0 - (variance / max_possible_variance))

        return uniformity

    def _calculate_overall_consistency(self) -> float:
        """Calcule la consistance générale du système"""
        if len(self.history) < 10:
            return 0.3

        factors = []

        # Consistance de force
        if self.force_history:
            force_consistency = 1.0 - np.std(list(self.force_history))
            factors.append(force_consistency * 0.4)

        # Consistance de momentum
        if self.momentum_history:
            momentum_consistency = 1.0 / (1.0 + np.std(list(self.momentum_history)))
            factors.append(momentum_consistency * 0.3)

        # Consistance temporelle
        timing_analysis = self._analyze_timing_patterns()
        timing_consistency = timing_analysis.get('timing_consistency', 0.5)
        factors.append(timing_consistency * 0.3)

        return np.mean(factors) if factors else 0.3

    def _calculate_prediction_confidence(self) -> float:
        """Calcule la confiance globale des prédictions"""
        overall_consistency = self._calculate_overall_consistency()
        data_sufficiency = min(len(self.history) / 50, 1.0)  # Plus de données = plus de confiance

        return (overall_consistency * 0.7 + data_sufficiency * 0.3)

    def _random_physics_prediction(self, count: int) -> List[Dict]:
        """Prédiction aléatoire avec justification physique"""
        predictions = []
        numbers = np.random.choice(37, size=count, replace=False)

        for num in numbers:
            predictions.append({
                'number': int(num),
                'confidence': 0.15,
                'reasoning': 'Données insuffisantes - prédiction aléatoire'
            })

        return predictions

    def display_physics_analysis(self):
        """Affiche l'analyse physique complète"""
        print("=== ANALYSE PHYSIQUE DE LA ROULETTE ===")

        if len(self.history) < 5:
            print("Données insuffisantes pour l'analyse physique.")
            print("Ajoutez au moins 5 résultats pour commencer l'analyse.")
            return

        analysis = self.get_physics_analysis()

        print(f"Historique: {len(self.history)} tirages")
        print(f"Confiance prédiction: {analysis['prediction_confidence']:.1%}")
        print(f"Consistance générale: {analysis['overall_consistency']:.1%}")

        # Force analysis
        force = analysis['force_analysis']
        print(f"\n=== ANALYSE DE FORCE ===")
        print(f"Force moyenne: {force['avg_force']:.2f}")
        print(f"Force récente: {force['recent_force']:.2f}")
        print(f"Tendance force: {force['force_trend']:+.3f}")
        print(f"Consistance: {force['consistency']:.1%}")

        # Sector analysis
        print(f"\n=== ANALYSE PAR SECTEUR ===")
        for sector, data in analysis['sector_analysis'].items():
            if data['deviation'] > 0.1:
                status = "CHAUD" if data['deviation'] > 0 else "FROID"
                print(f"{sector}: {status} ({data['deviation']:+.2f}, {data['hits']} hits)")

        # Momentum analysis
        momentum = analysis['momentum_analysis']
        print(f"\n=== ANALYSE DE MOMENTUM ===")
        print(f"Momentum moyen: {momentum['avg_momentum']:+.2f}")
        print(f"Momentum récent: {momentum['recent_momentum']:+.2f}")
        print(f"Accélération: {momentum['momentum_acceleration']:+.3f}")

        # Prédictions
        predictions = self.predict_by_physics(7)
        print(f"\n=== PRÉDICTIONS PHYSIQUES ===")
        for i, pred in enumerate(predictions, 1):
            print(f"{i}. Numéro {pred['number']:2d} - Confiance: {pred['confidence']:.1%}")
            print(f"   Raison: {pred['reasoning']}")

        return predictions

    def calculate_launch_force_estimate(self, recent_count: int = 5) -> Dict:
        """
        Calcule une estimation détaillée de la force de lancer

        Args:
            recent_count: Nombre de tirages récents à analyser

        Returns:
            Dict avec l'analyse détaillée de la force
        """
        if len(self.history) < recent_count:
            return {"error": "Pas assez de données pour estimer la force"}

        recent_numbers = list(self.history)[-recent_count:]
        force_indicators = []

        # Analyser chaque transition
        for i in range(len(recent_numbers) - 1):
            current_pos = self.wheel_layout.index(recent_numbers[i])
            next_pos = self.wheel_layout.index(recent_numbers[i + 1])

            # Distance angulaire
            distance = min(abs(next_pos - current_pos), 37 - abs(next_pos - current_pos))

            # Indicateurs de force basés sur la distance
            # Distance courte = force faible ou très forte (rebond)
            # Distance moyenne = force modérée
            # Distance longue = force élevée

            if distance <= 5:
                force_indicator = 0.3  # Force faible ou rebond
            elif distance <= 12:
                force_indicator = 0.6  # Force modérée
            elif distance <= 20:
                force_indicator = 0.9  # Force élevée
            else:
                force_indicator = 0.4  # Force très élevée (peut causer des rebonds)

            force_indicators.append({
                'transition': f"{recent_numbers[i]} → {recent_numbers[i+1]}",
                'distance': distance,
                'force_estimate': force_indicator,
                'positions': f"{current_pos} → {next_pos}"
            })

        # Calculer la force moyenne et la tendance
        forces = [fi['force_estimate'] for fi in force_indicators]
        avg_force = np.mean(forces)
        force_trend = np.polyfit(range(len(forces)), forces, 1)[0] if len(forces) > 1 else 0
        force_stability = 1.0 - np.std(forces)

        # Prédire la prochaine force
        if force_trend > 0.05:
            next_force_prediction = "CROISSANTE"
        elif force_trend < -0.05:
            next_force_prediction = "DÉCROISSANTE"
        else:
            next_force_prediction = "STABLE"

        return {
            'recent_transitions': force_indicators,
            'average_force': avg_force,
            'force_trend': force_trend,
            'force_stability': force_stability,
            'next_force_prediction': next_force_prediction,
            'recommended_sectors': self._get_force_based_sectors(avg_force, force_trend)
        }

    def _get_force_based_sectors(self, avg_force: float, force_trend: float) -> List[str]:
        """Recommande des secteurs basés sur l'analyse de force"""
        recommendations = []

        if avg_force < 0.4:  # Force faible
            recommendations.append("voisins_zero")  # Secteur central
        elif avg_force > 0.7:  # Force élevée
            recommendations.append("tiers_cylindre")  # Secteur opposé
        else:  # Force modérée
            recommendations.append("orphelins")  # Secteurs intermédiaires

        # Ajuster selon la tendance
        if force_trend > 0.1:  # Force croissante
            recommendations.append("serie_5_8")
        elif force_trend < -0.1:  # Force décroissante
            recommendations.append("serie_0_2_3")

        return recommendations

    def get_wheel_bias_analysis(self) -> Dict:
        """Analyse les biais potentiels de la roue"""
        if len(self.history) < 50:
            return {"error": "Pas assez de données pour analyser les biais"}

        # Analyser la distribution par quadrants
        quadrants = {
            'Q1': list(range(0, 9)),   # 0-8
            'Q2': list(range(9, 18)),  # 9-17
            'Q3': list(range(18, 27)), # 18-26
            'Q4': list(range(27, 37))  # 27-36
        }

        quadrant_hits = {q: 0 for q in quadrants.keys()}
        recent_numbers = list(self.history)[-100:]  # 100 derniers tirages

        for number in recent_numbers:
            wheel_pos = self.wheel_layout.index(number)
            for quad, positions in quadrants.items():
                if wheel_pos in positions:
                    quadrant_hits[quad] += 1
                    break

        # Calculer les déviations
        expected_per_quadrant = len(recent_numbers) / 4
        bias_analysis = {}

        for quad, hits in quadrant_hits.items():
            deviation = (hits - expected_per_quadrant) / expected_per_quadrant
            bias_analysis[quad] = {
                'hits': hits,
                'expected': expected_per_quadrant,
                'deviation': deviation,
                'bias_level': 'FORT' if abs(deviation) > 0.3 else 'MODÉRÉ' if abs(deviation) > 0.15 else 'FAIBLE'
            }

        return {
            'quadrant_analysis': bias_analysis,
            'most_biased_quadrant': max(bias_analysis.items(), key=lambda x: abs(x[1]['deviation'])),
            'overall_bias_strength': np.mean([abs(data['deviation']) for data in bias_analysis.values()])
        }

    def predict_next_sector(self) -> Dict:
        """Prédit le secteur le plus probable pour le prochain tirage"""
        if len(self.history) < 10:
            return {"error": "Pas assez de données"}

        force_analysis = self._analyze_force_patterns()
        momentum_analysis = self._analyze_momentum_patterns()

        # Estimer la distance du prochain tirage
        estimated_distance = self._estimate_next_distance(force_analysis, momentum_analysis)

        last_number = self.history[-1]
        last_position = self.wheel_layout.index(last_number)

        # Calculer les positions probables
        probable_positions = []
        for offset in range(-2, 3):  # ±2 positions autour de l'estimation
            pos = (last_position + int(estimated_distance) + offset) % 37
            probable_positions.append(pos)

        # Convertir en numéros
        probable_numbers = [self.wheel_layout[pos] for pos in probable_positions]

        # Analyser quels secteurs contiennent ces numéros
        sector_scores = {}
        for sector, numbers in self.sector_groups.items():
            score = sum(1 for num in probable_numbers if num in numbers)
            sector_scores[sector] = score / len(probable_numbers)

        # Trouver le secteur le plus probable
        best_sector = max(sector_scores.items(), key=lambda x: x[1])

        return {
            'estimated_distance': estimated_distance,
            'probable_numbers': probable_numbers,
            'sector_scores': sector_scores,
            'recommended_sector': best_sector[0],
            'sector_confidence': best_sector[1],
            'sector_numbers': self.sector_groups[best_sector[0]]
        }


def main():
    """Interface utilisateur principale pour le prédicteur physique"""
    print("=== PRÉDICTEUR PHYSIQUE DE ROULETTE ===")
    print("Analyse la force de lancer et les patterns physiques")

    predictor = RoulettePhysicsPredictor()

    print("\n=== COMMANDES DISPONIBLES ===")
    print("- Entrez un numéro (0-36): Ajoute le résultat à l'analyse")
    print("- 'batch': Saisie de plusieurs numéros d'un coup")
    print("- 'predict' ou 'p': Prédictions basées sur la physique")
    print("- 'force' ou 'f': Analyse détaillée de la force de lancer")
    print("- 'sector' ou 's': Prédiction du prochain secteur")
    print("- 'bias' ou 'b': Analyse des biais de la roue")
    print("- 'analysis' ou 'a': Analyse physique complète")
    print("- 'clear': Effacer l'historique")
    print("- 'help': Afficher l'aide")
    print("- 'quit': Quitter")

    while True:
        try:
            print(f"\nHistorique: {len(predictor.history)} tirages")
            if predictor.history:
                print(f"Derniers numéros: {list(predictor.history)[-5:]}")

            user_input = input("\n> ").strip().lower()

            if user_input in ['quit', 'exit', 'q']:
                print("Au revoir!")
                break

            elif user_input == 'help':
                print("\n=== AIDE - PRÉDICTEUR PHYSIQUE ===")
                print("Ce système analyse les aspects physiques de la roulette:")
                print("• Force de lancer: Consistance et intensité du lancer")
                print("• Momentum: Tendance directionnelle de la bille")
                print("• Secteurs: Zones géographiques de la roue")
                print("• Biais: Déviations par rapport au hasard pur")
                print("\nL'analyse devient plus précise avec plus de données.")
                print("Recommandé: au moins 20-30 tirages pour des prédictions fiables.")

            elif user_input in ['predict', 'p']:
                predictions = predictor.predict_by_physics(7)
                print(f"\n=== PRÉDICTIONS PHYSIQUES ===")
                for i, pred in enumerate(predictions, 1):
                    print(f"{i}. Numéro {pred['number']:2d} - Confiance: {pred['confidence']:.1%}")
                    print(f"   {pred['reasoning']}")

            elif user_input in ['force', 'f']:
                force_analysis = predictor.calculate_launch_force_estimate()
                if "error" in force_analysis:
                    print(f"Erreur: {force_analysis['error']}")
                else:
                    print(f"\n=== ANALYSE DE FORCE DE LANCER ===")
                    print(f"Force moyenne estimée: {force_analysis['average_force']:.2f}")
                    print(f"Stabilité de force: {force_analysis['force_stability']:.1%}")
                    print(f"Tendance: {force_analysis['next_force_prediction']}")

                    print(f"\nTransitions récentes:")
                    for trans in force_analysis['recent_transitions'][-3:]:
                        print(f"  {trans['transition']} - Distance: {trans['distance']} - Force: {trans['force_estimate']:.2f}")

                    print(f"\nSecteurs recommandés: {', '.join(force_analysis['recommended_sectors'])}")

            elif user_input in ['sector', 's']:
                sector_pred = predictor.predict_next_sector()
                if "error" in sector_pred:
                    print(f"Erreur: {sector_pred['error']}")
                else:
                    print(f"\n=== PRÉDICTION DE SECTEUR ===")
                    print(f"Distance estimée: {sector_pred['estimated_distance']:.1f} positions")
                    print(f"Secteur recommandé: {sector_pred['recommended_sector']}")
                    print(f"Confiance: {sector_pred['sector_confidence']:.1%}")
                    print(f"Numéros du secteur: {sector_pred['sector_numbers']}")
                    print(f"Numéros probables: {sector_pred['probable_numbers']}")

            elif user_input in ['bias', 'b']:
                bias_analysis = predictor.get_wheel_bias_analysis()
                if "error" in bias_analysis:
                    print(f"Erreur: {bias_analysis['error']}")
                else:
                    print(f"\n=== ANALYSE DES BIAIS DE ROUE ===")
                    print(f"Force de biais globale: {bias_analysis['overall_bias_strength']:.2f}")

                    most_biased = bias_analysis['most_biased_quadrant']
                    print(f"Quadrant le plus biaisé: {most_biased[0]} ({most_biased[1]['bias_level']})")

                    print(f"\nAnalyse par quadrant:")
                    for quad, data in bias_analysis['quadrant_analysis'].items():
                        print(f"  {quad}: {data['hits']} hits ({data['deviation']:+.1%}) - {data['bias_level']}")

            elif user_input in ['analysis', 'a']:
                predictor.display_physics_analysis()

            elif user_input == 'clear':
                confirm = input("Confirmer l'effacement de l'historique? (o/n): ")
                if confirm.lower() in ['o', 'oui', 'y', 'yes']:
                    predictor.history.clear()
                    predictor.timestamps.clear()
                    predictor.force_history.clear()
                    predictor.momentum_history.clear()
                    predictor.velocity_patterns.clear()
                    print("Historique effacé.")

            elif user_input == 'batch':
                print("Entrez plusieurs numéros séparés par des espaces ou des virgules:")
                batch_input = input("> ")
                numbers = []
                for item in batch_input.replace(',', ' ').split():
                    try:
                        num = int(item)
                        if 0 <= num <= 36:
                            numbers.append(num)
                    except ValueError:
                        pass

                if numbers:
                    for num in numbers:
                        predictor.add_result(num)
                    print(f"Ajouté {len(numbers)} numéros: {numbers}")
                else:
                    print("Aucun numéro valide trouvé.")

            else:
                try:
                    number = int(user_input)
                    predictor.add_result(number)
                    print(f"Numéro {number} ajouté.")

                    # Analyse automatique après 10 numéros
                    if len(predictor.history) >= 10 and len(predictor.history) % 5 == 0:
                        print("\n--- Analyse automatique ---")
                        predictions = predictor.predict_by_physics(5)
                        print("Top 5 prédictions:")
                        for i, pred in enumerate(predictions[:5], 1):
                            print(f"  {i}. {pred['number']} ({pred['confidence']:.1%})")

                except ValueError:
                    print("Veuillez entrer un numéro valide (0-36) ou une commande.")

        except KeyboardInterrupt:
            print("\nAu revoir!")
            break
        except Exception as e:
            print(f"Erreur: {e}")


if __name__ == "__main__":
    main()