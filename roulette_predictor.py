#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prédicteur de Roulette Anglaise
Analyse les données historiques pour prédire les prochains numéros
"""

import numpy as np
import pandas as pd
from collections import Counter, deque
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import warnings
import os
from typing import List
warnings.filterwarnings('ignore')

# Importer le module de reconnaissance d'images si disponible
try:
    from image_recognition import RouletteImageRecognition
    IMAGE_RECOGNITION_AVAILABLE = True
except ImportError:
    IMAGE_RECOGNITION_AVAILABLE = False

class RoulettePredictor:
    def __init__(self, max_history=1000):
        self.numbers = list(range(37))  # 0-36 pour la roulette anglaise
        self.history = deque(maxlen=max_history)
        self.frequency_counter = Counter()
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False
        
        # Initialiser la reconnaissance d'images si disponible
        if IMAGE_RECOGNITION_AVAILABLE:
            try:
                self.image_recognizer = RouletteImageRecognition()
                print("Module de reconnaissance d'images active")
            except Exception as e:
                self.image_recognizer = None
                print(f"Erreur initialisation reconnaissance: {e}")
        else:
            self.image_recognizer = None
        
    def add_number(self, number):
        """Ajoute un nouveau numéro à l'historique"""
        if 0 <= number <= 36:
            self.history.append(number)
            self.frequency_counter[number] += 1
            if len(self.history) >= 10:
                self._retrain_model()
        else:
            raise ValueError("Le numéro doit être entre 0 et 36")
    
    def add_numbers(self, numbers):
        """Ajoute plusieurs numéros à l'historique"""
        for num in numbers:
            self.add_number(num)
    
    def process_image(self, image_path: str) -> dict:
        """Traite une image pour extraire les numéros"""
        if not self.image_recognizer:
            return {"error": "Module de reconnaissance d'images non disponible"}
        
        if not os.path.exists(image_path):
            return {"error": f"Image non trouvée: {image_path}"}
        
        try:
            results = self.image_recognizer.process_image(image_path)
            return results
        except Exception as e:
            return {"error": f"Erreur traitement image: {e}"}
    
    def add_numbers_from_image(self, image_path: str, auto_add: bool = True) -> dict:
        """Extrait et ajoute automatiquement les numéros d'une image"""
        results = self.process_image(image_path)
        
        if "error" in results:
            return results
        
        detected_numbers = results.get('all_detected', [])
        
        if auto_add and detected_numbers:
            initial_count = len(self.history)
            self.add_numbers(detected_numbers)
            added_count = len(self.history) - initial_count
            
            results['added_to_history'] = detected_numbers
            results['count_added'] = added_count
            results['total_history'] = len(self.history)
        
        return results
    
    def batch_process_images(self, folder_path: str, auto_add: bool = True) -> List[dict]:
        """Traite toutes les images d'un dossier"""
        if not self.image_recognizer:
            return [{"error": "Module de reconnaissance d'images non disponible"}]
        
        try:
            results = self.image_recognizer.batch_process_images(folder_path)
            
            if auto_add:
                all_numbers = []
                for result in results:
                    all_numbers.extend(result.get('all_detected', []))
                
                if all_numbers:
                    initial_count = len(self.history)
                    self.add_numbers(all_numbers)
                    added_count = len(self.history) - initial_count
                    
                    # Ajouter info globale
                    summary = {
                        'batch_summary': True,
                        'images_processed': len(results),
                        'total_numbers_found': len(all_numbers),
                        'numbers_added': added_count,
                        'final_history_size': len(self.history),
                        'all_numbers': all_numbers
                    }
                    results.append(summary)
            
            return results
            
        except Exception as e:
            return [{"error": f"Erreur traitement batch: {e}"}]
    
    def get_frequency_analysis(self):
        """Analyse des fréquences des numéros"""
        if not self.history:
            return {}
        
        total = len(self.history)
        analysis = {}
        
        # Fréquences absolues et relatives
        for num in self.numbers:
            count = self.frequency_counter[num]
            analysis[num] = {
                'count': count,
                'frequency': count / total if total > 0 else 0,
                'expected_freq': 1/37,  # Fréquence théorique
                'deviation': (count / total - 1/37) if total > 0 else 0
            }
        
        return analysis
    
    def get_hot_cold_numbers(self, threshold=0.1):
        """Identifie les numéros chauds et froids"""
        analysis = self.get_frequency_analysis()
        hot_numbers = []
        cold_numbers = []
        
        for num, data in analysis.items():
            if data['deviation'] > threshold:
                hot_numbers.append((num, data['frequency']))
            elif data['deviation'] < -threshold:
                cold_numbers.append((num, data['frequency']))
        
        # Trier par fréquence
        hot_numbers.sort(key=lambda x: x[1], reverse=True)
        cold_numbers.sort(key=lambda x: x[1])
        
        return hot_numbers, cold_numbers
    
    def analyze_patterns(self):
        """Analyse les patterns et séquences"""
        if len(self.history) < 5:
            return {}
        
        patterns = {
            'last_5': list(self.history)[-5:],
            'most_common': self.frequency_counter.most_common(10),
            'consecutive_patterns': self._find_consecutive_patterns(),
            'gap_analysis': self._analyze_gaps()
        }
        
        return patterns
    
    def _find_consecutive_patterns(self):
        """Trouve les patterns consécutifs"""
        patterns = {}
        history_list = list(self.history)
        
        for length in [2, 3]:
            pattern_counts = Counter()
            for i in range(len(history_list) - length + 1):
                pattern = tuple(history_list[i:i+length])
                pattern_counts[pattern] += 1
            patterns[f'length_{length}'] = pattern_counts.most_common(5)
        
        return patterns
    
    def _analyze_gaps(self):
        """Analyse les écarts entre les occurrences"""
        gaps = {}
        history_list = list(self.history)
        
        for num in range(37):
            positions = [i for i, x in enumerate(history_list) if x == num]
            if len(positions) > 1:
                num_gaps = [positions[i+1] - positions[i] for i in range(len(positions)-1)]
                gaps[num] = {
                    'avg_gap': np.mean(num_gaps) if num_gaps else 0,
                    'last_seen': len(history_list) - positions[-1] - 1 if positions else len(history_list),
                    'gaps': num_gaps[-5:]  # 5 derniers écarts
                }
        
        return gaps
    
    def _create_features(self, sequence_length=10):
        """Crée les features pour le modèle ML"""
        if len(self.history) < sequence_length + 1:
            return None, None
        
        history_list = list(self.history)
        X, y = [], []
        
        for i in range(len(history_list) - sequence_length):
            # Features basiques: les derniers numéros
            features = history_list[i:i+sequence_length]
            
            # Features statistiques
            recent = history_list[max(0, i-20):i+sequence_length]
            features.extend([
                np.mean(recent),
                np.std(recent),
                len(set(recent)),  # diversité
                recent.count(recent[-1]) if recent else 0,  # répétitions récentes
            ])
            
            # Features de position (pair/impair, rouge/noir, etc.)
            last_num = features[sequence_length-1]
            features.extend([
                last_num % 2,  # pair/impair
                1 if last_num in range(1, 19) else 0,  # manque/passe
                1 if last_num in [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36] else 0  # rouge
            ])
            
            X.append(features)
            y.append(history_list[i + sequence_length])
        
        return np.array(X), np.array(y)
    
    def _retrain_model(self):
        """Réentraîne le modèle avec les nouvelles données"""
        X, y = self._create_features()
        if X is not None and len(X) > 20:
            try:
                X_scaled = self.scaler.fit_transform(X)
                self.model.fit(X_scaled, y)
                self.is_trained = True
            except Exception:
                pass
    
    def predict_next_numbers(self, count=7):
        """Prédit les prochains numéros"""
        if len(self.history) < 10:
            return self._random_prediction(count)
        
        predictions = []
        
        # Méthode 1: Fréquences pondérées
        freq_pred = self._frequency_based_prediction(count)
        
        # Méthode 2: Analyse des patterns
        pattern_pred = self._pattern_based_prediction(count)
        
        # Méthode 3: Machine Learning (si entraîné)
        ml_pred = self._ml_prediction(count) if self.is_trained else []
        
        # Combiner les prédictions
        predictions = self._combine_predictions(freq_pred, pattern_pred, ml_pred, count)
        
        return predictions
    
    def _frequency_based_prediction(self, count):
        """Prédiction basée sur les fréquences"""
        analysis = self.get_frequency_analysis()
        hot_numbers, cold_numbers = self.get_hot_cold_numbers()
        
        # Mélange de numéros chauds et froids avec pondération
        candidates = []
        
        # Ajouter les numéros chauds avec plus de poids
        for num, freq in hot_numbers[:15]:
            candidates.extend([num] * max(1, int(freq * 100)))
        
        # Ajouter quelques numéros froids (retour à la moyenne)
        for num, freq in cold_numbers[:10]:
            candidates.extend([num] * max(1, int((1/37 - freq) * 50)))
        
        # Ajouter de la randomisation
        candidates.extend(np.random.choice(37, size=20))
        
        # Sélectionner sans répétition
        predictions = []
        while len(predictions) < count and candidates:
            num = np.random.choice(candidates)
            if num not in predictions:
                predictions.append(int(num))
            # Retirer quelques occurrences pour éviter la répétition
            candidates = [x for i, x in enumerate(candidates) if not (x == num and i < 3)]
        
        return predictions
    
    def _pattern_based_prediction(self, count):
        """Prédiction basée sur les patterns"""
        patterns = self.analyze_patterns()
        predictions = []
        
        # Analyser les patterns récents
        if len(self.history) >= 3:
            last_3 = tuple(list(self.history)[-3:])
            # Chercher des patterns similaires dans l'historique
            history_list = list(self.history)[:-1]  # Exclure le dernier
            
            similar_positions = []
            for i in range(len(history_list) - 2):
                if tuple(history_list[i:i+3]) == last_3:
                    if i + 3 < len(history_list):
                        similar_positions.append(history_list[i+3])
            
            if similar_positions:
                # Utiliser les numéros qui ont suivi des patterns similaires
                pattern_counter = Counter(similar_positions)
                for num, _ in pattern_counter.most_common(count):
                    if num not in predictions:
                        predictions.append(num)
        
        return predictions
    
    def _ml_prediction(self, count):
        """Prédiction par machine learning"""
        if not self.is_trained or len(self.history) < 10:
            return []
        
        try:
            # Préparer les features pour la prédiction
            recent = list(self.history)[-10:]
            recent_stats = list(self.history)[-20:]
            
            features = recent + [
                np.mean(recent_stats),
                np.std(recent_stats),
                len(set(recent_stats)),
                recent_stats.count(recent[-1]),
                recent[-1] % 2,
                1 if recent[-1] in range(1, 19) else 0,
                1 if recent[-1] in [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36] else 0
            ]
            
            X_pred = np.array([features])
            X_pred_scaled = self.scaler.transform(X_pred)
            
            # Faire plusieurs prédictions avec du bruit pour la diversité
            predictions = []
            for _ in range(count * 3):
                noise = np.random.normal(0, 0.1, X_pred_scaled.shape)
                pred = self.model.predict(X_pred_scaled + noise)[0]
                pred_rounded = int(round(pred)) % 37
                predictions.append(pred_rounded)
            
            # Retourner les plus fréquents sans répétition
            counter = Counter(predictions)
            result = []
            for num, _ in counter.most_common():
                if num not in result and len(result) < count:
                    result.append(num)
            
            return result
            
        except Exception:
            return []
    
    def _combine_predictions(self, freq_pred, pattern_pred, ml_pred, count):
        """Combine les différentes méthodes de prédiction"""
        combined = []
        all_predictions = []
        
        # Donner des poids différents aux méthodes
        weights = {'freq': 0.4, 'pattern': 0.3, 'ml': 0.3}
        
        # Ajouter les prédictions avec pondération
        for pred in freq_pred:
            all_predictions.extend([pred] * int(weights['freq'] * 10))
        
        for pred in pattern_pred:
            all_predictions.extend([pred] * int(weights['pattern'] * 10))
        
        for pred in ml_pred:
            all_predictions.extend([pred] * int(weights['ml'] * 10))
        
        # Si pas assez de prédictions, ajouter de l'aléatoire
        if not all_predictions:
            return self._random_prediction(count)
        
        # Sélectionner les plus probables sans répétition
        counter = Counter(all_predictions)
        for num, _ in counter.most_common():
            if num not in combined and len(combined) < count:
                combined.append(num)
        
        # Compléter avec de l'aléatoire si nécessaire
        while len(combined) < count:
            rand_num = np.random.randint(0, 37)
            if rand_num not in combined:
                combined.append(rand_num)
        
        return combined
    
    def _random_prediction(self, count):
        """Prédiction aléatoire comme fallback"""
        return list(np.random.choice(37, size=count, replace=False))
    
    def get_prediction_confidence(self, predictions):
        """Calcule un score de confiance pour les prédictions"""
        if not self.history or not predictions:
            return 0.0
        
        confidence_factors = []
        
        # Facteur 1: Taille de l'historique
        history_factor = min(len(self.history) / 100, 1.0)
        confidence_factors.append(history_factor * 0.3)
        
        # Facteur 2: Cohérence des fréquences
        freq_analysis = self.get_frequency_analysis()
        freq_variance = np.var([data['frequency'] for data in freq_analysis.values()])
        freq_factor = min(freq_variance * 10, 1.0)
        confidence_factors.append(freq_factor * 0.3)
        
        # Facteur 3: Présence de patterns
        patterns = self.analyze_patterns()
        pattern_factor = 0.5 if patterns.get('consecutive_patterns') else 0.2
        confidence_factors.append(pattern_factor * 0.2)
        
        # Facteur 4: Performance du modèle ML
        ml_factor = 0.7 if self.is_trained else 0.3
        confidence_factors.append(ml_factor * 0.2)
        
        total_confidence = sum(confidence_factors)
        return min(total_confidence, 0.95)  # Max 95% de confiance
    
    def display_analysis(self):
        """Affiche une analyse complète"""
        print("=== ANALYSE DE LA ROULETTE ===")
        print(f"Historique: {len(self.history)} tirages")
        
        if not self.history:
            print("Aucune donnée disponible.")
            return
        
        # Fréquences
        hot_numbers, cold_numbers = self.get_hot_cold_numbers()
        print(f"\nNuméros chauds: {[num for num, _ in hot_numbers[:5]]}")
        print(f"Numéros froids: {[num for num, _ in cold_numbers[:5]]}")
        
        # Patterns récents
        patterns = self.analyze_patterns()
        print(f"5 derniers numéros: {patterns.get('last_5', [])}")
        
        # Prédictions
        predictions = self.predict_next_numbers(7)
        confidence = self.get_prediction_confidence(predictions)
        
        print(f"\n=== PRÉDICTIONS ===")
        print(f"Prochains numéros probables: {predictions}")
        print(f"Niveau de confiance: {confidence:.1%}")
        
        return predictions, confidence


def main():
    """Interface utilisateur principale"""
    predictor = RoulettePredictor()
    
    print("=== PRÉDICTEUR DE ROULETTE ANGLAISE ===")
    print("Entrez les numéros historiques (0-36) ou tapez 'help' pour l'aide")
    
    while True:
        try:
            user_input = input("\n> ").strip().lower()
            
            if user_input in ['quit', 'exit', 'q']:
                break
            elif user_input == 'help':
                print("\nCommandes disponibles:")
                print("- Entrez un numéro (0-36): Ajoute le numéro à l'historique")
                print("- 'predict' ou 'p': Génère les prédictions")
                print("- 'analyse' ou 'a': Affiche l'analyse complète")
                print("- 'clear': Efface l'historique")
                print("- 'batch': Saisie de plusieurs numéros d'un coup")
                if IMAGE_RECOGNITION_AVAILABLE:
                    print("- 'image': Traiter une image de roulette")
                    print("- 'folder': Traiter toutes les images d'un dossier")
                print("- 'quit': Quitter le programme")
            elif user_input in ['predict', 'p']:
                predictions, confidence = predictor.display_analysis()
            elif user_input in ['analyse', 'a']:
                predictor.display_analysis()
            elif user_input == 'clear':
                predictor.history.clear()
                predictor.frequency_counter.clear()
                predictor.is_trained = False
                print("Historique effacé.")
            elif user_input == 'batch':
                print("Entrez plusieurs numéros séparés par des espaces ou des virgules:")
                batch_input = input("> ")
                numbers = []
                for item in batch_input.replace(',', ' ').split():
                    try:
                        num = int(item)
                        if 0 <= num <= 36:
                            numbers.append(num)
                    except ValueError:
                        pass
                
                if numbers:
                    predictor.add_numbers(numbers)
                    print(f"Ajouté {len(numbers)} numéros: {numbers}")
                else:
                    print("Aucun numéro valide trouvé.")
            elif user_input == 'image' and IMAGE_RECOGNITION_AVAILABLE:
                image_path = input("Chemin vers l'image: ").strip().strip('"')
                if image_path:
                    print(f"Traitement de l'image: {image_path}")
                    results = predictor.add_numbers_from_image(image_path)
                    
                    if "error" in results:
                        print(f"Erreur: {results['error']}")
                    else:
                        print(f"Numéros détectés: {results.get('all_detected', [])}")
                        if results.get('count_added', 0) > 0:
                            print(f"Ajoutés à l'historique: {results['count_added']} numéros")
                            print(f"Total historique: {results['total_history']} tirages")
                        else:
                            print("Aucun numéro ajouté à l'historique")
            elif user_input == 'folder' and IMAGE_RECOGNITION_AVAILABLE:
                folder_path = input("Chemin vers le dossier d'images: ").strip().strip('"')
                if folder_path:
                    print(f"Traitement du dossier: {folder_path}")
                    results = predictor.batch_process_images(folder_path)
                    
                    if results and "error" in results[0]:
                        print(f"Erreur: {results[0]['error']}")
                    else:
                        # Afficher le résumé
                        summary = None
                        for result in results:
                            if result.get('batch_summary'):
                                summary = result
                                break
                        
                        if summary:
                            print(f"Images traitées: {summary['images_processed']}")
                            print(f"Numéros trouvés: {summary['total_numbers_found']}")
                            print(f"Ajoutés à l'historique: {summary['numbers_added']}")
                            print(f"Total historique: {summary['final_history_size']} tirages")
                        
                        # Afficher les détails par image
                        print("\nDétails par image:")
                        for result in results:
                            if not result.get('batch_summary'):
                                filename = os.path.basename(result.get('file', 'inconnu'))
                                detected = result.get('all_detected', [])
                                print(f"  {filename}: {detected}")
            elif user_input in ['image', 'folder'] and not IMAGE_RECOGNITION_AVAILABLE:
                print("Module de reconnaissance d'images non disponible.")
                print("Installez: pip install opencv-python pytesseract pillow")
                print("Et Tesseract OCR: https://github.com/tesseract-ocr/tesseract")
            else:
                try:
                    number = int(user_input)
                    predictor.add_number(number)
                    print(f"Numéro {number} ajouté. Total: {len(predictor.history)} tirages")
                    
                    # Prédiction automatique après 10 numéros
                    if len(predictor.history) >= 10 and len(predictor.history) % 5 == 0:
                        print("\n--- Prédiction automatique ---")
                        predictions = predictor.predict_next_numbers(7)
                        confidence = predictor.get_prediction_confidence(predictions)
                        print(f"Prédictions: {predictions}")
                        print(f"Confiance: {confidence:.1%}")
                        
                except ValueError:
                    print("Veuillez entrer un numéro valide (0-36) ou une commande.")
                    
        except KeyboardInterrupt:
            print("\nAu revoir!")
            break
        except Exception as e:
            print(f"Erreur: {e}")


if __name__ == "__main__":
    main()