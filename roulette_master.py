#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface Maître pour le Système de Prédiction de Roulette
Combine tous les modules : prédiction statistique, analyse physique, Fibonacci, reconnaissance d'images
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Optional

# Importer tous les modules disponibles
try:
    from roulette_predictor import RoulettePredictor
    PREDICTOR_AVAILABLE = True
except ImportError:
    PREDICTOR_AVAILABLE = False
    print("⚠️ Module roulette_predictor non disponible")

try:
    from physics_predictor import RoulettePhysicsPredictor
    PHYSICS_AVAILABLE = True
except ImportError:
    PHYSICS_AVAILABLE = False
    print("⚠️ Module physics_predictor non disponible")

try:
    from fibonacci_roulette import FibonacciRouletteSystem
    FIBONACCI_AVAILABLE = True
except ImportError:
    FIBONACCI_AVAILABLE = False
    print("⚠️ Module fibonacci_roulette non disponible")

try:
    from image_recognition import RouletteImageRecognition
    IMAGE_RECOGNITION_AVAILABLE = True
except ImportError:
    IMAGE_RECOGNITION_AVAILABLE = False
    print("⚠️ Module image_recognition non disponible")

class RouletteMasterSystem:
    def __init__(self):
        """Initialise le système maître de roulette"""
        self.session_start = datetime.now()
        self.numbers_history = []

        # Initialiser les modules disponibles
        self.predictor = RoulettePredictor() if PREDICTOR_AVAILABLE else None
        self.physics = RoulettePhysicsPredictor() if PHYSICS_AVAILABLE else None
        self.fibonacci = None  # Initialisé à la demande
        self.image_recognizer = RouletteImageRecognition() if IMAGE_RECOGNITION_AVAILABLE else None

        # État de la session
        self.current_mode = "prediction"  # prediction, physics, fibonacci
        self.auto_analysis = True

        print("=== SYSTÈME MAÎTRE DE ROULETTE ===")
        print(f"Session démarrée: {self.session_start.strftime('%H:%M:%S')}")
        self._display_available_modules()

    def _display_available_modules(self):
        """Affiche les modules disponibles"""
        print("\n=== MODULES DISPONIBLES ===")
        if self.predictor:
            print("✅ Prédicteur statistique et ML")
        if self.physics:
            print("✅ Analyseur physique (force de lancer)")
        if FIBONACCI_AVAILABLE:
            print("✅ Système de Fibonacci")
        if self.image_recognizer:
            print("✅ Reconnaissance d'images")

        if not any([self.predictor, self.physics, FIBONACCI_AVAILABLE, self.image_recognizer]):
            print("❌ Aucun module disponible")

    def add_number(self, number: int):
        """Ajoute un numéro à tous les systèmes actifs"""
        if not (0 <= number <= 36):
            raise ValueError("Le numéro doit être entre 0 et 36")

        self.numbers_history.append({
            'number': number,
            'timestamp': datetime.now(),
            'session_position': len(self.numbers_history) + 1
        })

        # Synchroniser avec tous les modules
        if self.predictor:
            self.predictor.add_number(number)

        if self.physics:
            self.physics.add_result(number)

        print(f"✅ Numéro {number} ajouté (Total: {len(self.numbers_history)} tirages)")

        # Analyse automatique
        if self.auto_analysis and len(self.numbers_history) >= 10 and len(self.numbers_history) % 5 == 0:
            self._auto_analysis()

    def _auto_analysis(self):
        """Effectue une analyse automatique"""
        print("\n🔍 === ANALYSE AUTOMATIQUE ===")

        # Prédictions rapides
        if self.predictor:
            predictions = self.predictor.predict_next_numbers(5)
            print(f"📊 Prédictions statistiques: {predictions[:3]}")

        if self.physics and len(self.numbers_history) >= 10:
            physics_preds = self.physics.predict_by_physics(3)
            physics_numbers = [p['number'] for p in physics_preds]
            print(f"⚡ Prédictions physiques: {physics_numbers}")

            # Force de lancer
            force_analysis = self.physics.calculate_launch_force_estimate()
            if "error" not in force_analysis:
                print(f"💪 Force estimée: {force_analysis['average_force']:.2f} ({force_analysis['next_force_prediction']})")

    def get_comprehensive_analysis(self) -> Dict:
        """Retourne une analyse complète de tous les modules"""
        analysis = {
            'session_info': {
                'start_time': self.session_start,
                'duration': datetime.now() - self.session_start,
                'total_numbers': len(self.numbers_history),
                'recent_numbers': [entry['number'] for entry in self.numbers_history[-10:]]
            },
            'statistical_analysis': None,
            'physics_analysis': None,
            'combined_predictions': []
        }

        # Analyse statistique
        if self.predictor and len(self.numbers_history) >= 5:
            try:
                hot_numbers, cold_numbers = self.predictor.get_hot_cold_numbers()
                patterns = self.predictor.analyze_patterns()
                predictions = self.predictor.predict_next_numbers(7)
                confidence = self.predictor.get_prediction_confidence(predictions)

                analysis['statistical_analysis'] = {
                    'hot_numbers': hot_numbers[:5],
                    'cold_numbers': cold_numbers[:5],
                    'recent_patterns': patterns.get('last_5', []),
                    'predictions': predictions,
                    'confidence': confidence
                }
            except Exception as e:
                analysis['statistical_analysis'] = {'error': str(e)}

        # Analyse physique
        if self.physics and len(self.numbers_history) >= 10:
            try:
                physics_analysis = self.physics.get_physics_analysis()
                physics_predictions = self.physics.predict_by_physics(7)
                force_analysis = self.physics.calculate_launch_force_estimate()

                analysis['physics_analysis'] = {
                    'force_analysis': force_analysis,
                    'physics_predictions': physics_predictions,
                    'overall_analysis': physics_analysis
                }
            except Exception as e:
                analysis['physics_analysis'] = {'error': str(e)}

        # Prédictions combinées
        combined_predictions = self._get_combined_predictions()
        analysis['combined_predictions'] = combined_predictions

        return analysis

    def _get_combined_predictions(self) -> List[Dict]:
        """Combine les prédictions de tous les modules"""
        all_predictions = {}

        # Prédictions statistiques
        if self.predictor and len(self.numbers_history) >= 5:
            try:
                stat_predictions = self.predictor.predict_next_numbers(7)
                for i, num in enumerate(stat_predictions):
                    if num not in all_predictions:
                        all_predictions[num] = {'sources': [], 'total_score': 0}
                    all_predictions[num]['sources'].append('statistical')
                    all_predictions[num]['total_score'] += (7 - i) * 0.3  # Poids décroissant
            except:
                pass

        # Prédictions physiques
        if self.physics and len(self.numbers_history) >= 10:
            try:
                physics_predictions = self.physics.predict_by_physics(7)
                for i, pred in enumerate(physics_predictions):
                    num = pred['number']
                    if num not in all_predictions:
                        all_predictions[num] = {'sources': [], 'total_score': 0}
                    all_predictions[num]['sources'].append('physics')
                    # Utiliser la confiance du prédicteur physique
                    all_predictions[num]['total_score'] += pred['confidence'] * (7 - i) * 0.4
            except:
                pass

        # Trier par score total
        sorted_predictions = sorted(
            all_predictions.items(),
            key=lambda x: x[1]['total_score'],
            reverse=True
        )

        # Formater les résultats
        result = []
        for num, data in sorted_predictions[:7]:
            result.append({
                'number': num,
                'score': data['total_score'],
                'sources': data['sources'],
                'confidence': min(data['total_score'] / 2.0, 1.0)  # Normaliser
            })

        return result

    def display_comprehensive_analysis(self):
        """Affiche une analyse complète formatée"""
        analysis = self.get_comprehensive_analysis()

        print("\n" + "="*60)
        print("🎯 ANALYSE COMPLÈTE DU SYSTÈME DE ROULETTE")
        print("="*60)

        # Informations de session
        session = analysis['session_info']
        duration = session['duration']
        print(f"📅 Session: {duration.seconds//3600:02d}h{(duration.seconds//60)%60:02d}m")
        print(f"🎲 Tirages: {session['total_numbers']}")
        print(f"📈 Récents: {session['recent_numbers']}")

        # Analyse statistique
        if analysis['statistical_analysis']:
            stat = analysis['statistical_analysis']
            if 'error' not in stat:
                print(f"\n📊 === ANALYSE STATISTIQUE ===")
                hot_nums = [str(num) for num, _ in stat['hot_numbers']]
                cold_nums = [str(num) for num, _ in stat['cold_numbers']]
                print(f"🔥 Numéros chauds: {', '.join(hot_nums)}")
                print(f"❄️  Numéros froids: {', '.join(cold_nums)}")
                print(f"🎯 Confiance: {stat['confidence']:.1%}")

        # Analyse physique
        if analysis['physics_analysis']:
            phys = analysis['physics_analysis']
            if 'error' not in phys:
                print(f"\n⚡ === ANALYSE PHYSIQUE ===")
                force = phys['force_analysis']
                if 'error' not in force:
                    print(f"💪 Force moyenne: {force['average_force']:.2f}")
                    print(f"📈 Tendance: {force['next_force_prediction']}")
                    print(f"🎯 Stabilité: {force['force_stability']:.1%}")

                    if force['recommended_sectors']:
                        print(f"🎯 Secteurs recommandés: {', '.join(force['recommended_sectors'])}")

        # Prédictions combinées
        if analysis['combined_predictions']:
            print(f"\n🔮 === PRÉDICTIONS COMBINÉES ===")
            for i, pred in enumerate(analysis['combined_predictions'][:5], 1):
                sources_str = '+'.join(pred['sources'])
                print(f"{i}. Numéro {pred['number']:2d} - Confiance: {pred['confidence']:.1%} ({sources_str})")

        print("="*60)

    def start_fibonacci_mode(self, base_bet: float = 1.0):
        """Démarre le mode Fibonacci"""
        if not FIBONACCI_AVAILABLE:
            print("❌ Module Fibonacci non disponible")
            return False

        try:
            self.fibonacci = FibonacciRouletteSystem(base_bet)
            self.current_mode = "fibonacci"
            print(f"✅ Mode Fibonacci activé (mise de base: {base_bet}€)")
            return True
        except Exception as e:
            print(f"❌ Erreur initialisation Fibonacci: {e}")
            return False

    def process_image(self, image_path: str):
        """Traite une image pour extraire les numéros"""
        if not self.image_recognizer:
            return {"error": "Module de reconnaissance d'images non disponible"}

        try:
            results = self.image_recognizer.process_image(image_path)
            if 'all_detected' in results and results['all_detected']:
                print(f"🖼️ Image traitée: {len(results['all_detected'])} numéros détectés")
                for num in results['all_detected']:
                    self.add_number(num)
                return results
            else:
                print("🖼️ Aucun numéro détecté dans l'image")
                return results
        except Exception as e:
            return {"error": f"Erreur traitement image: {e}"}

    def get_quick_recommendations(self) -> Dict:
        """Retourne des recommandations rapides"""
        if len(self.numbers_history) < 5:
            return {"error": "Pas assez de données (minimum 5 tirages)"}

        recommendations = {
            'top_numbers': [],
            'avoid_numbers': [],
            'sectors': [],
            'betting_strategy': '',
            'confidence_level': 'LOW'
        }

        # Obtenir les prédictions combinées
        combined = self._get_combined_predictions()
        if combined:
            recommendations['top_numbers'] = [pred['number'] for pred in combined[:3]]
            avg_confidence = sum(pred['confidence'] for pred in combined[:3]) / 3

            if avg_confidence > 0.7:
                recommendations['confidence_level'] = 'HIGH'
            elif avg_confidence > 0.4:
                recommendations['confidence_level'] = 'MEDIUM'

        # Recommandations de secteurs physiques
        if self.physics and len(self.numbers_history) >= 10:
            try:
                sector_pred = self.physics.predict_next_sector()
                if 'error' not in sector_pred:
                    recommendations['sectors'] = [sector_pred['recommended_sector']]
            except:
                pass

        # Stratégie de mise
        if recommendations['confidence_level'] == 'HIGH':
            recommendations['betting_strategy'] = "Mises agressives sur les top 3"
        elif recommendations['confidence_level'] == 'MEDIUM':
            recommendations['betting_strategy'] = "Mises modérées, diversifiées"
        else:
            recommendations['betting_strategy'] = "Mises conservatrices, attendre plus de données"

        return recommendations

    def export_session_data(self, filename: Optional[str] = None) -> str:
        """Exporte les données de session"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"roulette_session_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=== SESSION DE ROULETTE ===\n")
                f.write(f"Début: {self.session_start}\n")
                f.write(f"Durée: {datetime.now() - self.session_start}\n")
                f.write(f"Total tirages: {len(self.numbers_history)}\n\n")

                f.write("=== HISTORIQUE DES NUMÉROS ===\n")
                for entry in self.numbers_history:
                    f.write(f"{entry['session_position']:3d}. {entry['number']:2d} - {entry['timestamp'].strftime('%H:%M:%S')}\n")

                f.write("\n=== ANALYSE FINALE ===\n")
                analysis = self.get_comprehensive_analysis()

                if analysis['combined_predictions']:
                    f.write("Prédictions finales:\n")
                    for i, pred in enumerate(analysis['combined_predictions'][:5], 1):
                        f.write(f"  {i}. {pred['number']} ({pred['confidence']:.1%})\n")

            return f"✅ Session exportée vers {filename}"
        except Exception as e:
            return f"❌ Erreur export: {e}"


def main():
    """Interface utilisateur principale du système maître"""
    print("🎰 Bienvenue dans le Système Maître de Roulette")
    print("Combinant analyse statistique, physique et stratégies de mise")

    # Initialiser le système
    try:
        system = RouletteMasterSystem()
    except Exception as e:
        print(f"❌ Erreur initialisation: {e}")
        return

    print(f"\n=== COMMANDES PRINCIPALES ===")
    print("📝 Saisie de données:")
    print("  • Numéro (0-36) : Ajouter un résultat")
    print("  • 'batch' : Saisie multiple")
    print("  • 'image' : Traiter une image")

    print("\n🔍 Analyses:")
    print("  • 'analysis' ou 'a' : Analyse complète")
    print("  • 'quick' ou 'q' : Recommandations rapides")
    print("  • 'stats' : Statistiques détaillées")
    print("  • 'physics' : Analyse physique")

    print("\n💰 Stratégies:")
    print("  • 'fibonacci' : Mode Fibonacci")
    print("  • 'recommendations' : Conseils de mise")

    print("\n⚙️ Utilitaires:")
    print("  • 'export' : Exporter la session")
    print("  • 'clear' : Effacer l'historique")
    print("  • 'help' : Aide détaillée")
    print("  • 'quit' : Quitter")

    # Boucle principale
    while True:
        try:
            # Affichage du statut
            if system.numbers_history:
                recent = [str(entry['number']) for entry in system.numbers_history[-5:]]
                print(f"\n📊 Historique ({len(system.numbers_history)}): {' → '.join(recent)}")

            user_input = input(f"\n[{system.current_mode.upper()}] > ").strip().lower()

            if user_input in ['quit', 'exit', 'q']:
                # Proposer l'export avant de quitter
                if system.numbers_history:
                    export_choice = input("💾 Exporter la session avant de quitter? (o/n): ")
                    if export_choice.lower() in ['o', 'oui', 'y', 'yes']:
                        result = system.export_session_data()
                        print(result)
                print("👋 Au revoir!")
                break

            elif user_input == 'help':
                print("\n📚 === AIDE DÉTAILLÉE ===")
                print("Ce système combine plusieurs approches de prédiction:")
                print("• 📊 Statistique: Fréquences, patterns, machine learning")
                print("• ⚡ Physique: Force de lancer, momentum, secteurs")
                print("• 💰 Fibonacci: Gestion progressive des mises")
                print("• 🖼️ Images: Reconnaissance automatique des résultats")
                print("\nPlus vous ajoutez de données, plus les prédictions s'améliorent.")
                print("Recommandé: minimum 20-30 tirages pour des analyses fiables.")

            elif user_input in ['analysis', 'a']:
                system.display_comprehensive_analysis()

            elif user_input in ['quick', 'q']:
                recommendations = system.get_quick_recommendations()
                if 'error' in recommendations:
                    print(f"❌ {recommendations['error']}")
                else:
                    print(f"\n⚡ === RECOMMANDATIONS RAPIDES ===")
                    print(f"🎯 Top numéros: {recommendations['top_numbers']}")
                    print(f"🎪 Secteurs: {recommendations['sectors']}")
                    print(f"💰 Stratégie: {recommendations['betting_strategy']}")
                    print(f"📈 Confiance: {recommendations['confidence_level']}")

            elif user_input == 'stats':
                if system.predictor and len(system.numbers_history) >= 5:
                    system.predictor.display_analysis()
                else:
                    print("❌ Pas assez de données pour les statistiques")

            elif user_input == 'physics':
                if system.physics and len(system.numbers_history) >= 10:
                    system.physics.display_physics_analysis()
                else:
                    print("❌ Pas assez de données pour l'analyse physique (minimum 10)")

            elif user_input == 'fibonacci':
                if system.start_fibonacci_mode():
                    base_bet = input("💰 Mise de base (défaut 1€): ").strip()
                    try:
                        base_bet = float(base_bet) if base_bet else 1.0
                        system.start_fibonacci_mode(base_bet)
                    except ValueError:
                        system.start_fibonacci_mode(1.0)

            elif user_input == 'image':
                if system.image_recognizer:
                    image_path = input("🖼️ Chemin vers l'image: ").strip().strip('"')
                    if image_path:
                        result = system.process_image(image_path)
                        if 'error' in result:
                            print(f"❌ {result['error']}")
                else:
                    print("❌ Module de reconnaissance d'images non disponible")

            elif user_input == 'batch':
                print("📝 Entrez plusieurs numéros séparés par des espaces:")
                batch_input = input("> ")
                numbers = []
                for item in batch_input.replace(',', ' ').split():
                    try:
                        num = int(item)
                        if 0 <= num <= 36:
                            numbers.append(num)
                    except ValueError:
                        pass

                if numbers:
                    for num in numbers:
                        system.add_number(num)
                    print(f"✅ {len(numbers)} numéros ajoutés")
                else:
                    print("❌ Aucun numéro valide trouvé")

            elif user_input == 'export':
                result = system.export_session_data()
                print(result)

            elif user_input == 'clear':
                confirm = input("⚠️ Confirmer l'effacement de l'historique? (o/n): ")
                if confirm.lower() in ['o', 'oui', 'y', 'yes']:
                    system.numbers_history.clear()
                    if system.predictor:
                        system.predictor.history.clear()
                        system.predictor.frequency_counter.clear()
                    if system.physics:
                        system.physics.history.clear()
                        system.physics.timestamps.clear()
                    print("✅ Historique effacé")

            else:
                # Essayer d'interpréter comme un numéro
                try:
                    number = int(user_input)
                    system.add_number(number)
                except ValueError:
                    print("❌ Commande non reconnue. Tapez 'help' pour l'aide.")

        except KeyboardInterrupt:
            print("\n👋 Session interrompue. Au revoir!")
            break
        except Exception as e:
            print(f"❌ Erreur: {e}")


if __name__ == "__main__":
    main()