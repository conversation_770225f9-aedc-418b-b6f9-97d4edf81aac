# Prédicteur de Roulette Anglaise avec Reconnaissance d'Images

Un logiciel Python avancé qui prédit les prochains numéros de la roulette anglaise en analysant les données historiques et en supportant la reconnaissance automatique d'images.

## Fonctionnalités

### Prédiction Intelligente
- ✅ **Analyse statistique** : Fréquences, numéros chauds/froids
- ✅ **Détection de patterns** : Séquences répétitives, écarts
- ✅ **Machine Learning adaptatif** : Random Forest qui s'améliore avec les données
- ✅ **Prédiction de 7 numéros** comme demandé
- ✅ **Niveau de confiance** calculé dynamiquement

### Reconnaissance d'Images (NOUVEAU!)
- 🖼️ **OCR avancé** : Détection automatique des numéros
- 🎯 **Zones spécialisées** : Roue, historique, numéro gagnant
- 📁 **Traitement par lot** : Analysez plusieurs images d'un coup
- 🔧 **Preprocessing intelligent** : Amélioration automatique des images

## Installation

### Dépendances de base
```bash
pip install numpy pandas scikit-learn
```

### Pour la reconnaissance d'images (optionnel)
```bash
pip install opencv-python pytesseract pillow
```

### Tesseract OCR
**Windows :**
1. Téléchargez depuis : https://github.com/tesseract-ocr/tesseract
2. Installez dans `C:\Program Files\Tesseract-OCR\`
3. Ajoutez au PATH système

**Alternative :** Le programme détecte automatiquement Tesseract dans les dossiers communs.

## Utilisation

### Mode Interactif Standard
```bash
python roulette_predictor.py
```

**Commandes disponibles :**
- Tapez un numéro (0-36) pour l'ajouter
- `predict` ou `p` : Générer les prédictions
- `analyse` ou `a` : Analyse complète
- `batch` : Saisie de plusieurs numéros
- `clear` : Effacer l'historique

### Mode Reconnaissance d'Images
```bash
python roulette_predictor.py
```

**Nouvelles commandes :**
- `image` : Analyser une image de roulette
- `folder` : Traiter toutes les images d'un dossier

### Utilisation Programmatique
```python
from roulette_predictor import RoulettePredictor

# Créer le prédicteur
predictor = RoulettePredictor()

# Méthode classique
predictor.add_numbers([12, 5, 18, 22, 3])
predictions = predictor.predict_next_numbers(7)

# Avec images
results = predictor.add_numbers_from_image("screenshot.png")
print(f"Numéros détectés: {results['all_detected']}")
```

## Types d'Images Supportés

### 📸 Captures d'Écran
- Interface de casino en ligne
- Applications mobile de roulette
- Streams vidéo de roulette

### 🎯 Zones Détectées
1. **Roue de roulette** : Numéro dans la zone centrale
2. **Historique** : Tableaux de numéros précédents
3. **Numéro gagnant** : Affichage du dernier résultat
4. **OCR général** : Tous les numéros visibles

### 📁 Formats Supportés
- PNG, JPG, JPEG, BMP, TIFF, GIF
- Résolution : Minimum 200x200, optimal 800x600+
- Contraste élevé recommandé

## Performance

### Seuils de Prédiction
- **1+ numéros** : Prédictions basiques
- **10+ numéros** : Analyse complète activée
- **50+ numéros** : Précision optimale

### Vitesse de Traitement
- **Images individuelles** : 1-3 secondes
- **Batch (10 images)** : 5-15 secondes
- **Historique 100+ numéros** : <1 seconde pour les prédictions

## Exemples d'Utilisation

### Scenario 1 : Casino en Ligne
```bash
# Prenez une capture d'écran du casino
# Lancez le prédicteur
python roulette_predictor.py

# Dans le programme :
> image
Chemin vers l'image: C:\Users\<USER>\casino.png
Numéros détectés: [12, 5, 18, 22, 3, 36]
Ajoutés à l'historique: 6 numéros
> predict
Prédictions: [15, 7, 29, 14, 8, 31, 20]
Confiance: 45%
```

### Scenario 2 : Analyse d'Historique
```bash
# Dossier avec plusieurs captures
> folder
Chemin vers le dossier d'images: C:\Users\<USER>\
Images traitées: 25
Numéros trouvés: 180
Total historique: 180 tirages
> analyse
=== ANALYSE COMPLÈTE ===
Numéros chauds: [18, 7, 22]
Numéros froids: [1, 13, 35]
Prédictions: [18, 25, 7, 14, 31, 6, 29]
Confiance: 67%
```

## Tests

### Test Automatique
```bash
python test_roulette.py
```

### Test de Reconnaissance
```bash
python image_recognition.py
```

## Conseils d'Optimisation

### Pour de Meilleures Détections
1. **Qualité d'image** : Captures nettes, bien éclairées
2. **Contraste élevé** : Fond foncé, texte clair
3. **Résolution** : Au moins 800x600 pixels
4. **Zone focalisée** : Cropper sur les zones de numéros

### Pour de Meilleures Prédictions
1. **Plus de données** : Au moins 50 numéros historiques
2. **Données récentes** : Privilégier les tirages récents
3. **Contexte** : Même casino/table si possible
4. **Validation** : Comparer avec les résultats réels

## Limitations

⚠️ **Avertissement** : Ce logiciel est à des fins éducatives et d'analyse. La roulette étant un jeu de hasard, aucune prédiction n'est garantie à 100%.

### Reconnaissance d'Images
- Dépend de la qualité de l'image
- Peut nécessiter des ajustements selon l'interface
- OCR peut avoir des erreurs sur images floues

### Prédictions
- Basées sur des patterns statistiques
- Plus efficaces avec beaucoup de données
- La roulette reste fondamentalement aléatoire

## Support

Pour des questions ou problèmes :
1. Vérifiez l'installation des dépendances
2. Testez avec les images d'exemple
3. Consultez les logs d'erreur

## Structure des Fichiers

```
Roulette/
├── roulette_predictor.py    # Programme principal
├── image_recognition.py     # Module reconnaissance
├── test_roulette.py        # Tests automatiques
├── README.md              # Ce fichier
└── images/                # Dossier pour vos images
```

Bon jeu et que les prédictions soient avec vous ! 🎲🎯