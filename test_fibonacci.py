#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tests pour le système de Fibonacci à la roulette
"""

import unittest
import os
import json
from fibonacci_roulette import FibonacciRouletteSystem

class TestFibonacciRouletteSystem(unittest.TestCase):
    
    def setUp(self):
        """Configuration avant chaque test"""
        self.system = FibonacciRouletteSystem(base_bet=1.0, max_sequence_length=10)
    
    def test_fibonacci_sequence_generation(self):
        """Test de la génération de la séquence de Fibonacci"""
        expected = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55]
        self.assertEqual(self.system.fibonacci_sequence, expected)
    
    def test_initial_state(self):
        """Test de l'état initial du système"""
        self.assertEqual(self.system.current_position, 0)
        self.assertEqual(self.system.get_current_bet_amount(), 1.0)
        self.assertEqual(self.system.total_profit, 0.0)
        self.assertEqual(self.system.wins, 0)
        self.assertEqual(self.system.losses, 0)
    
    def test_winning_bet_rouge(self):
        """Test d'un pari gagnant sur rouge"""
        # Numéro rouge : 23
        result = self.system.place_bet('rouge', 23)
        
        self.assertTrue(result['is_winner'])
        self.assertEqual(result['bet_amount'], 1.0)
        self.assertEqual(result['profit'], 1.0)  # Gain = mise × (2-1)
        self.assertEqual(self.system.current_position, 0)  # Retour au début
        self.assertEqual(self.system.wins, 1)
        self.assertEqual(self.system.total_profit, 1.0)
    
    def test_losing_bet_rouge(self):
        """Test d'un pari perdant sur rouge"""
        # Numéro noir : 8
        result = self.system.place_bet('rouge', 8)
        
        self.assertFalse(result['is_winner'])
        self.assertEqual(result['bet_amount'], 1.0)
        self.assertEqual(result['profit'], -1.0)  # Perte = -mise
        self.assertEqual(self.system.current_position, 1)  # Avance dans la séquence
        self.assertEqual(self.system.losses, 1)
        self.assertEqual(self.system.total_profit, -1.0)
    
    def test_zero_always_loses(self):
        """Test que le zéro fait perdre tous les paris simples"""
        result = self.system.place_bet('rouge', 0)
        self.assertFalse(result['is_winner'])
        
        result = self.system.place_bet('noir', 0)
        self.assertFalse(result['is_winner'])
        
        result = self.system.place_bet('pair', 0)
        self.assertFalse(result['is_winner'])
    
    def test_fibonacci_progression(self):
        """Test de la progression dans la séquence de Fibonacci"""
        # Série de pertes
        self.system.place_bet('rouge', 2)  # Perte, position 1
        self.assertEqual(self.system.get_current_bet_amount(), 1.0)  # Fibonacci[1] = 1
        
        self.system.place_bet('rouge', 4)  # Perte, position 2
        self.assertEqual(self.system.get_current_bet_amount(), 2.0)  # Fibonacci[2] = 2
        
        self.system.place_bet('rouge', 6)  # Perte, position 3
        self.assertEqual(self.system.get_current_bet_amount(), 3.0)  # Fibonacci[3] = 3
        
        # Gain - retour au début
        result = self.system.place_bet('rouge', 1)  # Gain
        self.assertTrue(result['is_winner'])
        self.assertEqual(self.system.current_position, 0)
        self.assertEqual(self.system.get_current_bet_amount(), 1.0)
    
    def test_different_bet_types(self):
        """Test des différents types de paris"""
        # Test pair
        result = self.system.place_bet('pair', 14)
        self.assertTrue(result['is_winner'])
        
        # Test impair
        result = self.system.place_bet('impair', 15)
        self.assertTrue(result['is_winner'])
        
        # Test douzaine
        result = self.system.place_bet('douzaine1', 7)
        self.assertTrue(result['is_winner'])
        self.assertEqual(result['payout_multiplier'], 3)
        
        # Test colonne
        result = self.system.place_bet('colonne2', 11)
        self.assertTrue(result['is_winner'])
        self.assertEqual(result['payout_multiplier'], 3)
    
    def test_sequence_limit(self):
        """Test de la limite de séquence"""
        # Forcer la position à la limite
        self.system.current_position = self.system.max_sequence_length - 1
        
        result = self.system.place_bet('rouge', 2)  # Perte
        self.assertTrue(result.get('sequence_limit_reached', False))
        self.assertEqual(self.system.current_position, 0)  # Reset automatique
    
    def test_simulation(self):
        """Test de la simulation de séquence"""
        simulation = self.system.simulate_sequence('rouge', 3)
        
        self.assertEqual(len(simulation['steps']), 4)  # 3 pertes + 1 gain
        self.assertEqual(simulation['win_after_steps'], 3)
        
        # Vérifier que le profit final est positif
        self.assertGreater(simulation['final_profit'], 0)
        
        # Vérifier les montants des mises
        expected_bets = [1, 1, 2, 3]  # Fibonacci pour positions 0,1,2,3
        for i, step in enumerate(simulation['steps']):
            self.assertEqual(step['bet_amount'], expected_bets[i])
    
    def test_risk_analysis(self):
        """Test de l'analyse des risques"""
        risk = self.system.get_risk_analysis()
        
        self.assertIn('current_risk_level', risk)
        self.assertIn('max_possible_loss', risk)
        self.assertIn('break_even_probability', risk)
        self.assertIsInstance(risk['recommendations'], list)
        
        # Au début, le risque doit être faible
        self.assertEqual(risk['current_risk_level'], 'FAIBLE')
    
    def test_statistics(self):
        """Test des statistiques"""
        # Placer quelques paris
        self.system.place_bet('rouge', 1)  # Gain
        self.system.place_bet('rouge', 2)  # Perte
        self.system.place_bet('rouge', 3)  # Gain
        
        stats = self.system.get_statistics()
        
        self.assertEqual(stats['total_bets'], 3)
        self.assertEqual(stats['wins'], 2)
        self.assertEqual(stats['losses'], 1)
        self.assertAlmostEqual(stats['win_rate'], 66.67, places=1)
    
    def test_save_and_load(self):
        """Test de sauvegarde et chargement"""
        # Placer quelques paris
        self.system.place_bet('rouge', 1)
        self.system.place_bet('noir', 2)
        
        # Sauvegarder
        filename = "test_fibonacci_data.json"
        self.system.save_data(filename)
        self.assertTrue(os.path.exists(filename))
        
        # Créer un nouveau système et charger
        new_system = FibonacciRouletteSystem()
        load_result = new_system.load_data(filename)
        
        self.assertIn("chargées", load_result)
        self.assertEqual(new_system.total_bets, self.system.total_bets)
        self.assertEqual(new_system.total_profit, self.system.total_profit)
        
        # Nettoyer
        os.remove(filename)
    
    def test_invalid_inputs(self):
        """Test des entrées invalides"""
        # Numéro invalide
        with self.assertRaises(ValueError):
            self.system.place_bet('rouge', 37)
        
        with self.assertRaises(ValueError):
            self.system.place_bet('rouge', -1)
        
        # Type de pari invalide
        with self.assertRaises(ValueError):
            self.system.place_bet('invalid_type', 15)
    
    def test_profit_calculation(self):
        """Test du calcul des profits"""
        # Test avec paris simples (payout 2:1)
        result = self.system.place_bet('rouge', 1)  # Gain
        self.assertEqual(result['profit'], 1.0)  # 1€ × (2-1) = 1€
        
        # Reset pour test suivant
        self.system.reset_session()
        
        # Test avec douzaine (payout 3:1)
        result = self.system.place_bet('douzaine1', 5)  # Gain
        self.assertEqual(result['profit'], 2.0)  # 1€ × (3-1) = 2€


class TestFibonacciMath(unittest.TestCase):
    """Tests spécifiques aux calculs mathématiques"""
    
    def test_fibonacci_sequence_correctness(self):
        """Vérifier que la séquence de Fibonacci est correcte"""
        system = FibonacciRouletteSystem(max_sequence_length=20)
        expected = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987, 1597, 2584, 4181, 6765]
        self.assertEqual(system.fibonacci_sequence, expected)
    
    def test_profit_recovery(self):
        """Test que le système récupère bien les pertes avec un gain"""
        system = FibonacciRouletteSystem(base_bet=1.0)
        
        # Simuler une séquence de 5 pertes puis 1 gain
        total_losses = 0
        
        # 5 pertes consécutives
        for i in range(5):
            result = system.place_bet('rouge', 2)  # Noir (perte)
            total_losses += result['bet_amount']
        
        # 1 gain
        result = system.place_bet('rouge', 1)  # Rouge (gain)
        final_profit = result['profit']
        
        # Le profit final doit être positif et récupérer toutes les pertes
        self.assertGreater(final_profit, 0)
        self.assertGreater(system.total_profit, 0)


def run_demo():
    """Démonstration du système"""
    print("=== DÉMONSTRATION DU SYSTÈME DE FIBONACCI ===\n")
    
    system = FibonacciRouletteSystem(base_bet=1.0, max_sequence_length=10)
    
    print("Simulation d'une séquence typique:")
    print("Pari sur ROUGE avec mise de base 1€\n")
    
    # Séquence de démonstration
    demo_sequence = [
        (2, "Noir - PERDU"),
        (4, "Noir - PERDU"), 
        (6, "Noir - PERDU"),
        (1, "Rouge - GAGNÉ!")
    ]
    
    for number, description in demo_sequence:
        bet_amount = system.get_current_bet_amount()
        print(f"Position {system.current_position + 1}: Mise {bet_amount}€ sur ROUGE")
        
        result = system.place_bet('rouge', number)
        print(f"Numéro sorti: {number} ({description})")
        print(f"Résultat: {result['net_result']:+.2f}€")
        print(f"Profit total: {system.total_profit:+.2f}€")
        print("-" * 40)
    
    print(f"\n=== RÉSULTAT FINAL ===")
    stats = system.get_statistics()
    print(f"Total paris: {stats['total_bets']}")
    print(f"Gains: {stats['wins']} | Pertes: {stats['losses']}")
    print(f"Profit final: {stats['total_profit']:+.2f}€")
    print(f"Taux de réussite: {stats['win_rate']:.1f}%")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        run_demo()
    else:
        # Lancer les tests
        print("Lancement des tests du système de Fibonacci...\n")
        unittest.main(verbosity=2)
